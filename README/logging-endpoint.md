# Logging Endpoint

The logging endpoint allows the admin panel to send log messages to the server for centralized logging and monitoring.

## Endpoints

### POST /api/v1.0.0/logs

Send a single log entry to the server.

**Request Body:**
```json
{
  "level": "info",
  "message": "User clicked submit button",
  "metadata": {
    "buttonId": "submit-form",
    "formData": "..."
  },
  "source": "admin-panel",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "userId": 123,
  "clientId": 456,
  "sessionId": "abc123",
  "userAgent": "Mozilla/5.0...",
  "url": "/admin/questions",
  "action": "button_click",
  "category": "user_interaction"
}
```

**Required Fields:**
- `level`: Log level (error, warn, info, debug)
- `message`: Log message

**Optional Fields:**
- `metadata`: Additional structured data
- `source`: Source of the log (defaults to "admin-panel")
- `timestamp`: ISO timestamp (defaults to current time)
- `userId`: User ID (extracted from auth if not provided)
- `clientId`: Client ID (extracted from auth if not provided)
- `sessionId`: Session ID (extracted from auth if not provided)
- `userAgent`: User agent string (extracted from headers if not provided)
- `url`: Current URL (extracted from request if not provided)
- `action`: Action being performed
- `category`: Category of the log entry

**Response:**
```json
{
  "message": "Log entry recorded successfully",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "level": "info",
  "source": "admin-panel"
}
```

### POST /api/v1.0.0/logs/batch

Send multiple log entries in a single request for efficiency.

**Request Body:**
```json
{
  "logs": [
    {
      "level": "info",
      "message": "Page loaded",
      "action": "page_load",
      "category": "navigation"
    },
    {
      "level": "debug",
      "message": "API call started",
      "metadata": {
        "endpoint": "/api/v1.0.0/questions"
      }
    }
  ]
}
```

**Limits:**
- Maximum 100 log entries per batch
- Minimum 1 log entry per batch

**Response:**
```json
{
  "message": "Successfully processed 2 log entries",
  "processedCount": 2,
  "logs": [
    {
      "level": "info",
      "message": "Page loaded",
      "timestamp": "2024-01-15T10:30:00.000Z",
      "source": "admin-panel"
    },
    {
      "level": "debug",
      "message": "API call started",
      "timestamp": "2024-01-15T10:30:01.000Z",
      "source": "admin-panel"
    }
  ]
}
```

## Security

Both endpoints require authentication and are accessible to:
- admin
- manager
- super admin
- user (when accessing admin panel)

## Usage Examples

### Using the LoggingService

```typescript
import loggingService from '../services/loggingService';

// Simple logging
await loggingService.info('User logged in successfully');
await loggingService.error('Failed to save question', { questionId: 123 });

// User action logging
await loggingService.logUserAction('submit_form', 'question_management', {
  formType: 'new_question',
  questionId: 456
});

// API error logging
try {
  const response = await api.post('/questions', questionData);
} catch (error) {
  await loggingService.logApiError('/questions', error, questionData);
  throw error;
}

// Performance logging
const startTime = performance.now();
await someOperation();
const duration = performance.now() - startTime;
await loggingService.logPerformance('question_save', duration, {
  questionLength: questionData.text.length
});
```

### Manual API Calls

```typescript
import { ApiService } from '../services/api.service';

const apiService = new ApiService();

// Single log
await apiService.post('logs', {
  level: 'info',
  message: 'Custom log message',
  metadata: { customData: 'value' }
});

// Batch logs
await apiService.post('logsBatch', {
  logs: [
    { level: 'info', message: 'First log' },
    { level: 'debug', message: 'Second log' }
  ]
});
```

## Features

### Automatic Batching
The LoggingService automatically batches logs and sends them every 5 seconds to reduce server load.

### Error Priority
Error-level logs are sent immediately rather than being batched.

### Retry Logic
Failed log requests are retried up to 3 times before being discarded.

### Page Unload Handling
Logs are automatically flushed when the page is about to unload using the Beacon API for reliable delivery.

### Automatic Error Capture
The service automatically captures:
- Unhandled JavaScript errors
- Unhandled promise rejections

### Rich Metadata
Logs automatically include:
- User information (from authentication)
- Request metadata (IP, user agent, origin)
- Timestamps
- Session information

## Log Levels

- **error**: Critical errors that need immediate attention
- **warn**: Warning conditions that should be monitored
- **info**: General information about application flow
- **debug**: Detailed information for debugging purposes

## Best Practices

1. **Use appropriate log levels**: Don't log everything as 'info'
2. **Include relevant metadata**: Add context that will help with debugging
3. **Avoid logging sensitive data**: Don't log passwords, tokens, or PII
4. **Use structured logging**: Include action and category for better filtering
5. **Log user actions**: Track important user interactions for analytics
6. **Log API errors**: Always log failed API calls with context
7. **Log performance metrics**: Track slow operations

## Server-Side Logging

Logs are processed by the Winston logging system and can be:
- Displayed in console (development)
- Sent to CloudWatch (production)
- Stored in log files with rotation
- Integrated with monitoring systems

The logs include comprehensive metadata for filtering and analysis:
- User and client information
- Request details (IP, user agent, origin)
- Timestamps and source identification
- Custom metadata and categorization
