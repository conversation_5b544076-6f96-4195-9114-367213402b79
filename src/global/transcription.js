import env from './environment.js';
import { awsTranscribe, s3 } from '../global/aws.js';

const convertHttpToS3 = (url) => {
	// Convert HTTP URL to S3 URL
	const urlParts = url.split('/');
	const fileName = urlParts.pop().split('.'); // Get filename and split by dot

	// Extract UUID from URL path (should be the part before the filename)
	const uuid = urlParts[urlParts.length - 1]; // Get the last part of the path before filename

	const type = (
		url.match('raw') ? 'raw' :
			url.match('manual') ? 'manual' :
				url.match('video_parts') ? 'video_parts' :
					url.match('files/') ? 'files' :
						'converted'
	); // Determine the type of file

	const returnValue = `s3://${env.getProperty('aws.buckets.file_document')}/${type}/${type === 'video_parts' ? uuid + '/' : ''}` + fileName[0] + '.' + fileName[1]; // Use UUID instead of fileName[0]

	console.log('Converted HTTP to S3:', returnValue);

	return returnValue;
};

export const getJobName = (url) => {
	// Extract path from URL and replace / with - to create unique job name
	const urlParts = url.split('/');
	const pathParts = urlParts.slice(3); // Remove protocol and domain parts
	return pathParts.join('-').replace(/\./g, '_'); // Replace dots with underscores for AWS compatibility
};

export const startTranscriptionJob = async (answer) => {
	if (!answer.videoUrl)
		return answer;
	if (answer.transcriptionProcessing === 'complete' || answer.transcriptionProcessing === 'failed')
		return answer;

	// `transcription-${answer.id}-${Date.now()}`; // Create a unique job name
	let jobName = getJobName(answer.videoUrl); // Create a unique job name
	let mediaUri = convertHttpToS3(answer.videoUrl); // Use the video URL from the answer

	// find the file on the s3 bucket to check if it exists
	// const s3SearchParams = {
	// 	Bucket: env.getProperty('aws.buckets.file_document'),
	// 	Key: answer.videoUrl.split('/').pop(),
	// };
	// find the file on the s3 bucket to check if it exists
	// try {
	// 	const s3SearchResult = await s3.headObject(s3SearchParams).promise();
	// 	console.log('Transcriptions -> S3 search result for file:', s3SearchResult, s3SearchParams);
	// } catch (error) {
	// 	console.error('Error searching for file on S3:', error);

	// 	jobName = getJobName(answer.videoUrl + '-conversion-attempt'); // Create a unique job name
	// 	mediaUri = convertHttpToS3(answer.videoUrls.mp4); // Use the video URL from the answer

	// 	await deleteTranscriptionJob(answer); // Delete the transcription job if it exists

	// 	answer.transcriptionProcessing = 'failed'; // Mark as done
	// 	answer.showTranscribedSubtitles = false; // Show subtitles by default

	// 	await answer.save();
	// }

	await deleteTranscriptionJob(answer); // Delete the transcription job if it exists

	const awsTranscriptionParams = {
		// MediaSampleRateHertz: 48000,
		// MediaFormat: "mp4",
		TranscriptionJobName: jobName,
		Media: {
			MediaFileUri: mediaUri,
		},
		LanguageCode: 'en-US',
		// OutputBucketName: 'your-output-bucket-name', // Replace with your S3 bucket name
	};

	console.log('Transcription job parameters', awsTranscriptionParams);

	let errored = false;
	try {
		const data = await awsTranscribe.startTranscriptionJob(awsTranscriptionParams).promise();

		console.log('Transcription job started:', data);

		answer.transcriptionProcessing = 'processing'; // Update the answer's transcription processing status
		await answer.save();

		return answer;
	} catch (error) {
		errored = true;

		// console.error('Error starting transcription job:', error);

		answer.transcriptionProcessing = 'failed'; // Mark as done
		answer.showTranscribedSubtitles = false; // Show subtitles by default
		await answer.save();

		return answer;
	}
};

export const deleteTranscriptionJob = async (answer) => {
	const jobName = getJobName(answer.videoUrl); // Create a unique job name

	const params = {
		TranscriptionJobName: jobName,
	};

	try {
		const data = await awsTranscribe.deleteTranscriptionJob(params).promise();

		console.log('Transcription job deleted:', data);

		return data;
	} catch (error) {
		// console.error('Error deleting transcription job:', error);
	}
};

export const getTranscriptionJob = async (jobName) => {
	const params = {
		TranscriptionJobName: jobName,
	};

	try {
		const data = await awsTranscribe.getTranscriptionJob(params).promise();

		console.log('Transcription job details:', data);

		return data;
	} catch (error) {
		// console.error('Error getting transcription job:', error);
	}
};

// Export the function for use in other modules.
export default {
	startTranscriptionJob,
	getTranscriptionJob,
	getJobName
};
