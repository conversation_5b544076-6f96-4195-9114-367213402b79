import { useRef, useEffect } from 'react';

import Modal from 'shared/Modal';
import Button from 'shared/Button';

import { AnswersService } from 'services';

import classes from './EditTranscription.module.scss';

type AwsTranscriptionAudioSegment = {
  start_time: string;
  end_time: string;
  transcript: string;
};

interface AwsTranscription {
  items: any[];
  transcripts: any[];
  audio_segments: AwsTranscriptionAudioSegment[];
}

interface EditTranscriptionProps {
  handleClose: () => void;
  transcription: AwsTranscription;
  answerId: string;
  clientId: number;
  answersService: AnswersService;
  setAnswers: any;
}

// minutes:seconds
const formatTime = (time: string) => {
  const [seconds, milliseconds] = time.split('.');

  return Math.floor(+seconds / 60) + 'm ' + (+seconds % 60) + 's';
  // return seconds + 's';
}

export default function EditTranscription({
  handleClose,
  transcription,
  answerId,
  clientId,
  answersService,
  setAnswers,
}: EditTranscriptionProps) {
  const transcriptionRefs = useRef<(HTMLDivElement | null)[]>([]);
  const currentValues = useRef<{ [key: number]: string }>({});

  // Initialize current values from transcription
  useEffect(() => {
    transcription?.audio_segments?.forEach((segment, index) => {
      if (currentValues.current[index] === undefined) {
        currentValues.current[index] = segment.transcript;
      }
    });
  }, [transcription]);

  const handleTranscriptionUpdate = (index: number, value: string) => {
    // Store the current value in ref (no re-render)
    currentValues.current[index] = value;
  }

  const handleTranscriptionSave = () => {
    // Create updated transcription with current values from the contentEditable elements
    const updatedTranscription = {
      ...transcription,
      audio_segments: transcription.audio_segments.map((segment, index) => ({
        ...segment,
        transcript: currentValues.current[index] || segment.transcript
      }))
    };

    const payload = {
      id: answerId,
      clientId,
      transcription: updatedTranscription,
    }

    answersService.updateAnswer(payload, () => answersService.getAnswers(setAnswers)).then(
      handleClose
    )
  };

  return (
    <Modal contentWrapperStyle={{ maxWidth: "640px" }}>
      <div className={classes.editTranscription}>
        <div className={classes.title}>Edit Subtitles</div>
        <div className={classes.bodySection}>
          {transcription?.audio_segments?.map((segment, index) => (
            <div className={classes.transcription} key={`segment-${index}-${segment.start_time}`}>
              <strong>{formatTime(segment.start_time)}</strong>
              <div
                className={classes.input}
                contentEditable="true"
                placeholder={"Enter a transcription for this time period..."}
                ref={(el) => {
                  transcriptionRefs.current[index] = el;
                  // Set initial content only once when element is created
                  if (el && !el.dataset.initialized) {
                    el.textContent = segment.transcript;
                    el.dataset.initialized = 'true';
                  }
                }}
                onInput={(e) => handleTranscriptionUpdate(index, (e.target as HTMLDivElement).innerText)}
                suppressContentEditableWarning={true}
              />
            </div>
          ))}

          {!transcription?.audio_segments?.length && (
            <div>Subtitles are processing. Please try again in a few minutes.</div>
          )}

          <div className={classes.buttonWrapper}>
            <Button text="Cancel" customClass={classes.button} callback={handleClose} />
            {transcription?.audio_segments?.length && (
              <Button text="Save" customClass={classes.button} callback={handleTranscriptionSave} />
            )}
          </div>
        </div>
      </div>
    </Modal>
  )
}