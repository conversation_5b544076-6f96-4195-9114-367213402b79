import { FunctionComponent } from "react";
import NavLink from "../../components/NavLink";
import styles from "./Sidebar.module.scss";
import { useServiceContext } from "services/ServiceProvider";

import SuperAdminIcon from "assets/icons/super-admin.svg";
import ErrorPageIcon from "assets/icons/error-page.svg";
import PublicPagesIcon from "assets/icons/public-pages.svg";
import NewPageIcon from "assets/icons/new-page.svg";
import AnalyticsIcon from "assets/icons/analytics.svg";
import VideosIcon from "assets/icons/videos.svg";
import EmailsIcon from "assets/icons/emails.svg";
import FeedbackIcon from "assets/icons/feedback.svg";
import SettingsIcon from "assets/icons/settings.svg";
import AnalyticsChartIcon from "assets/icons/analytics-chart.svg";
import AnswersIcon from "assets/icons/answers.svg";
import QuestionsIcon from "assets/icons/questions.svg";
import TeamIcon from "assets/icons/team.svg";
import ApprovedIcon from "assets/icons/approved.svg";
import ReviewIcon from "assets/icons/review.svg";
import ArchiveIcon from "assets/icons/archive.svg";
import SharedIcon from "assets/icons/shared-list.svg";
import CompletedIcon from "assets/icons/completed.svg";
import DraftsIcon from "assets/icons/drafts.svg";
import APIIcon from "assets/icons/api.svg";
import { UserInterface } from "interfaces";
import { cn } from "utils/utils";
import { useUIStore } from "hooks/zustand/uiStore";

export type SidebarType = {
  user?: UserInterface;
  className?: string;
};

const Sidebar: FunctionComponent<SidebarType> = ({
  user,
  className = "",
}) => {
  const uiType = useUIStore(state => state.uiType);
  const { isSidebarOpen, setSidebarOpen } = useUIStore(state => state);
  const { adminStatsService } = useServiceContext();

  // Sidebar links content - reused for both desktop and mobile
  const sidebarContent = (
    <>
      <div className={`${styles.sidebarLinksContainer}`}>
        {
          user?.accessLevel === "super admin" && <>
            <NavLink title="Super Admin Panel" href="/super" icon={SuperAdminIcon} />
            <NavLink title="Public Pages" href="/super" parentRoute="/super" icon={PublicPagesIcon} suboption />
            <NavLink title="New Page" href="/super/new" parentRoute="/super" icon={NewPageIcon} suboption />
            <NavLink title="Partners" href="/partners" icon={APIIcon} />
            <NavLink title="Error Page" href="/error-page" icon={ErrorPageIcon} />
          </>
        }
        <NavLink title="Analytics" href="/voter-analytics" icon={AnalyticsChartIcon} />
        <NavLink title="Engagements" href="/voter-analytics" parentRoute="/voter-analytics" icon={AnalyticsIcon} suboption />
        <NavLink title="Videos" href="/voter-analytics/videos" parentRoute="/voter-analytics" icon={VideosIcon} suboption />
        <NavLink title="Emails" href="/voter-analytics/emails" parentRoute="/voter-analytics" icon={EmailsIcon} suboption />
        {
          user?.accessLevel !== "super admin" && <>
            <NavLink title="Questions" href="/questions-list" icon={QuestionsIcon} />
            <NavLink title="Approved" href="/questions-list" parentRoute="/questions-list" icon={ApprovedIcon} suboption />
            <NavLink title="Review" href="/questions-list/review" parentRoute="/questions-list" icon={ReviewIcon} suboption />
            <NavLink title="Archive" href="/questions-list/archive" parentRoute="/questions-list" icon={ArchiveIcon} suboption />
            <NavLink title="Shared" href="/questions-list/shared" parentRoute="/questions-list" icon={SharedIcon} suboption />
            <NavLink title="Answers" href="/answers-list" icon={AnswersIcon} />
            <NavLink title="Uploaded" href="/answers-list" parentRoute="/answers-list" icon={CompletedIcon} suboption />
            <NavLink title="Drafts" href="/answers-list/drafts" parentRoute="/answers-list" icon={DraftsIcon} suboption />
            <NavLink title="Team" href="/team" icon={TeamIcon} />
          </>
        }
      </div>
      {
        user?.accessLevel !== "super admin" && <>
          <div className={`${styles.shortSidebarLinksContain}`}>
            <NavLink title="Feedback" aTag icon={FeedbackIcon} />
            <NavLink title="Settings" href="/edit-client" icon={SettingsIcon} />
          </div>
        </>
      }
    </>
  );

  const handleShadowClick = () => {
    adminStatsService?.trackEvent('Sidebar', 'close_sidebar_by_overlay');
    setSidebarOpen(false);
  };

  return (
    <>
      <div
        className={cn(styles.shadow, isSidebarOpen ? "block" : "hidden")}
        onClick={handleShadowClick}
      />
      <div className={cn(
        styles.sidebar,
        className,
        uiType === "pdf" && "hidden",
        styles.sidebarDesktop
      )}>
        {sidebarContent}
      </div>

      <div className={cn(
        styles.sidebarMobile,
        isSidebarOpen && styles.open
      )}>
        <div className={`${styles.sidebarMobileContent}`}>
          {sidebarContent}
        </div>
      </div>
    </>
  );
};

export default Sidebar;
