import express from 'express';
import logger from '../global/log.js';
// import requireAll from 'require-all';
import errorUtil from '../utils/errorUtil.js';
import sessionService from '../services/sessionService.js';
import { __dirname } from '../utils/sysUtil.js';
// import { injectDocsRoute } from '../docs/index.js';
import fs from 'fs';
import path from 'path';

const router = express.Router();
const routes = [];
const securityConstraints = [];

// Retrieve routes
const routesDir = path.join(__dirname(import.meta.url), '.');
const routeFiles = fs.readdirSync(routesDir).filter(file => file.endsWith('Route.js'));

for (const file of routeFiles) {
  const routeModule = (await import(path.join(routesDir, file)));
  const route = routeModule.default || routeModule;

  routes.push(route.router);
  securityConstraints.push(...route.securityConstraints);
}

// CORS Headers
router.use( ( request, response, next ) => {
	const requestOrigin = ( request.headers.origin || '' ).toLowerCase();

	if ( process.env.NODE_ENV == 'development' || process.env.NODE_ENV == 'staging' )
		response.setHeader( 'Access-Control-Allow-Origin', '*' );
	else if ( requestOrigin.includes( '.repd.us' ) || requestOrigin.includes( 'repd-temp-analytics' ) )
		response.setHeader( 'Access-Control-Allow-Origin', request.headers.origin );
	else
		response.setHeader( 'Access-Control-Allow-Origin', null ); // *.repd.us

	response.setHeader( 'Access-Control-Allow-Headers',
		'Origin, Accept, Accept-Version, Content-Length, Content-Type, Authorization'
	);

	next();
} );

// Authentication Logic
router.use( ( request, response, next ) => {
	// if (!securityConstraints || securityConstraints.length === 0)
	// 	return next();

	// Skip authentication for partner API routes
	if (request.path.startsWith('/partner-api/')) {
		return next();
	}

	const scs = securityConstraints.filter(
		sc => sc.methods.includes( request.method ) && request.path.match( sc.regex )
	);

	const authIsMandatory = !scs.some( sc => !( sc.accessLevels || [] ).length > 0 );
	const isAdmin = [request.headers.origin].includes( 'admin' ) || request.cookies.isAdmin;

	var tokenValue = request.headers[ 'authorization' ] || request.headers[ 'Authorization' ];

	if ( scs.length === 0 && !tokenValue ) {
		request.auth = { user: {}, session: {} };

		next();
		return;
	}

	if ( !tokenValue && authIsMandatory ) {
		logger.error( 'debug', `Error, a missing token is invoking: ${ request.url }` );

		return response.status( 401 ).json( {
			code: 'ERROR_SESSION_TOKEN_MISSING',
			message: 'No authorization token.'
		} );
	}

	if (!tokenValue || tokenValue === '') tokenValue = 'null';

	return sessionService.getValidByToken(
		tokenValue,
		isAdmin,
		request.query.clientId || request.cookies.clientId || request.cookies.clientid,
		request.headers.userId || request.headers.userid
	)
		.then( session => {
			const user = ( session || {} ).user || {};

			const hasAccessLevel = scs.length === 0 ||
				scs.some( sc => !sc.accessLevels ||
					( sc.accessLevels.length === 0 ? true : sc.accessLevels.includes( user.accessLevel ) )
				);

			if ( !hasAccessLevel )
				return errorUtil.rejectPromiseWithErrorWithResponse( 403, {
					code: 'ERROR_SESSION_TOKEN_USER_HAS_NO_ACCESS',
					message: `User with token "${ tokenValue }" cannot access this resource.`
				} );

			request.auth = { user: user, session: session };

			next();
		} )
		.catch( error => {
			if ( authIsMandatory )
				errorUtil.sendErrorResponse( response, error );
			else next();
		} );
} );

// Routes
router.get( '/health_check', ( request, response, next ) => {
	console.log('health check ping');

	return response.status(200).send('OK');
} );

router.get('/obsceneWordsList.json', async (request, response, next) => {
  try {
    const obsceneWordsList = (await import('../helpers/obsceneWordsList.js')).default;
    const harmfulWordsList = (await import('../helpers/harmfulWordsList.js')).default;

    if (Array.isArray(obsceneWordsList) && Array.isArray(harmfulWordsList)) {
      response.status(200).json(obsceneWordsList.concat(harmfulWordsList));
    } else {
      response.status(500).json({ error: 'Invalid data format' });
    }
  } catch (error) {
    next(error);
  }
});

routes.forEach( route => router.use( '/', route ) );

// Configurations
// import('../docs/index.js').then(docs => docs.injectRoute(router));
// injectDocsRoute(router, '/docs');
import('../models/index.js');

export default router;
