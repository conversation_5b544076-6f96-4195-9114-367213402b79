import express from 'express';
import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid'; // Updated import statement
import jwt from 'jsonwebtoken';
import { celebrate } from 'celebrate';

import service from '../services/userService.js';
import validation from '../validations/userValidation.js';

import { conditionalCelebrate } from '../helpers/validationHelper.js';

const router = express.Router();
import session from '../global/session.js';

// End-points
router.post( '/api/v1.0.0/users', conditionalCelebrate(), ( request, response, next ) => { // celebrate( validation.create ),
	return bcrypt.hash( request.body.password, 10 ).then( ( password ) => {
		const adminAccessLeves = [ 'super admin', 'admin', 'manager' ];
		const defaultAccessLevel = 'user';
		const accessLevel = request.body.accessLevel && adminAccessLeves.includes(request.auth.user.accessLevel) ? request.body.accessLevel || 'manager' : defaultAccessLevel;

		const criteria = {
			...request.body,
			email: request.body.email.toLowerCase(),
			accessLevel,
			passwordEncrypted: password,
			verificationToken: uuidv4()
		};

		return(
			service.create( criteria, request.auth )
				.then( dto => {
					response.status( 201 ).json( {
						'message': 'User Created',
						'totalEntries': 1,
						'data': [ dto ]
					} );
				} )
				.catch( next )
		);
	} );
} );

router.post( '/api/v1.0.0/users/magic', conditionalCelebrate(), async ( request, response, next ) => { // celebrate( validation.create ),
	const result = await service.generateMagicLink(request.body, request.query);

	if ( result ) {
		response.status( 200 ).json( {
			'message': 'Magic Link Generated'
		} );
	} else {
		response.status( 400 ).json( {
			'message': 'User Not Found'
		} );
	}
} );

router.post( '/api/v1.0.0/users/magic/confirm', conditionalCelebrate(), ( request, response, next ) => { // celebrate( validation.create ),
	try {
		const decoded = jwt.verify(request.body.token, session.secret);
		const criteria = {
			email: decoded.email,
			accessLevel: 'user',
			isVerified: true
		};
		return(
			service.createFromMagicLink( criteria, request.auth )
				.then( dto => {
					response.status( 200 ).json( {
						'message': 'User Retrieved',
						'totalEntries': 1,
						'data': [ dto ]
					} );
				} )
				.catch( next )
		);
	} catch ( error ) {
		console.log( error );
		response.status( 400 ).json( { error: error } );
	}
} );

router.get( '/api/v1.0.0/users/me', ( request, response, next ) => {
	return(
		service.getById( request.auth.user.id, request.auth )
			.then( dto => {
				response.status( 200 ).json( {
					'message': 'User Retrieved',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

// celebrate( validation.update )
router.put( '/api/v1.0.0/users/me', conditionalCelebrate(), ( request, response, next ) => {
	const criteria = {
		...request.body,
		id: request.auth?.user?.id,
		accessLevel: request?.headers?.origin?.match(/admin/i) ? request.body.accessLevel : 'visitor',
		...( request.body.email && { email: request.body.email.toLowerCase() } ),
		enabled: true
	};

	if (request.auth.user.accessLevel !== "super admin" && request.body.accessLevel === "super admin") {
		return response.status( 403 ).json( {
			'message': 'Not Authorized',
			'totalEntries': 0,
			'data': []
		} );
	}

	return(
		service.update( criteria, request.auth )
			.then( dto => {
				response.status( 200 ).json( {
					'message': 'User Updated',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

router.put( '/api/v1.0.0/users', conditionalCelebrate(), ( request, response, next ) => {
	const criteria = {
		...request.body,
		...( request.body.email && { email: request.body.email.toLowerCase() } ),
	};

	if (request.auth.user.accessLevel !== "super admin" && request.body.accessLevel === "super admin") {
		return response.status( 403 ).json( {
			'message': 'Not Authorized',
			'totalEntries': 0,
			'data': []
		} );
	}

	return(
		service.update( criteria, request.auth )
			.then( dto => {
				response.status( 200 ).json( {
					'message': 'User Updated',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

router.put( '/api/v1.0.0/users/verify/:token', conditionalCelebrate(), ( request, response, next ) => {
	return (
		service.verify( request.params.token )
			.then( dto => {
				response.status( 200 ).json( {
					'message': 'User Updated',
					'totalEntries': 1,
					'data': [ dto ]
				} );
			} )
			.catch( next )
	);
} );

router.get( '/api/v1.0.0/users/verify/:token', ( request, response, next ) => {
	service.verify( request.params.token );

	response.send( `
      <html lang="en">
        <head><title>Redirecting you...</title></head>
        <body>Verified <script>// window.location = 'https://repd.us/verify?token=${ request.params.token }'</script></body>
      </html>
    ` );
} );

//  celebrate( validation.block )
router.put( '/api/v1.0.0/users/block', conditionalCelebrate(), async ( request, response, next ) => {
	const { id, isBlocked } = request.body;
	const clientId = request.query.clientId;

	const result = await service.blockForClient(  id, clientId, isBlocked ? 'unblock' : 'block' );

	if ( result )
		response.status( 200 ).json( {
			'message': 'User updated'
		} );
	else
		response.status( 400 ).json( {
			'message': 'Bad request'
		} );
} );

export default {
	router: router,

	securityConstraints: [ {
		regex: '/api/v.*/users/me',
		methods: [ 'GET', 'PUT' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/users',
		methods: [ 'PUT' ],
		accessLevels: [ 'super admin' ]
	}, {
		regex: '/api/v.*/users',
		methods: [ 'POST' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/users/verify',
		methods: [ 'PUT' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/users/magic',
		methods: [ 'POST' ],
		accessLevels: []
	}, {
		regex: '/api/v.*/users/magic/confirm',
		methods: [ 'POST' ],
		accessLevels: []
	},  {
		regex: '/api/v.*/users',
		methods: [ 'DELETE' ],
		accessLevels: [ 'admin', 'manager', 'super admin' ]
	},  {
		regex: '/api/v.*/users',
		methods: [ 'PUT' ],
		accessLevels: [ 'admin', 'manager', 'super admin', 'guest admin' ]
	} ]
};
