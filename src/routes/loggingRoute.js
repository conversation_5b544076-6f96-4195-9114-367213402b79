import express from 'express';
import { celebrate, Joi } from 'celebrate';
import logger from '../global/log.js';
import { asyncRouteWithErrorLogging } from '../utils/errorLogUtil.js';

const router = express.Router();

/**
 * POST /api/v1.0.0/logs
 * Accept log entries from the admin panel and log them to the server
 */
router.post('/api/v1.0.0/logs', 
  celebrate({
    body: Joi.object({
      level: Joi.string().valid('error', 'warn', 'info', 'debug').required(),
      message: Joi.string().required(),
      metadata: Joi.object().optional(),
      source: Joi.string().default('admin-panel'),
      timestamp: Joi.date().optional(),
      userId: Joi.number().integer().optional(),
      clientId: Joi.number().integer().optional(),
      sessionId: Joi.string().optional(),
      userAgent: Joi.string().optional(),
      url: Joi.string().optional(),
      action: Joi.string().optional(),
      category: Joi.string().optional()
    })
  }),
  asyncRouteWithErrorLogging(async (req, res) => {
    try {
      const {
        level,
        message,
        metadata = {},
        source,
        timestamp,
        userId,
        clientId,
        sessionId,
        userAgent,
        url,
        action,
        category
      } = req.body;

      // Extract user information from auth if available
      const authUser = req.auth?.user;
      const finalUserId = userId || authUser?.id;
      const finalClientId = clientId || authUser?.client?.id;

      // Create comprehensive log metadata
      const logMetadata = {
        source,
        userId: finalUserId,
        clientId: finalClientId,
        sessionId: sessionId || req.auth?.session?.id,
        userAgent: userAgent || req.headers['user-agent'],
        url: url || req.originalUrl,
        action,
        category,
        timestamp: timestamp || new Date().toISOString(),
        originalMetadata: metadata,
        requestId: req.headers['x-request-id'],
        ip: req.ip || req.connection.remoteAddress,
        origin: req.headers.origin
      };

      // Log the message with the specified level
      logger.log(level, `[${source}] ${message}`, logMetadata);

      // Return success response
      res.status(200).json({
        message: 'Log entry recorded successfully',
        timestamp: logMetadata.timestamp,
        level,
        source
      });

    } catch (error) {
      // Log the error in logging the log (meta!)
      logger.error('Error processing log request', {
        error: error.message,
        stack: error.stack,
        requestBody: req.body
      });

      res.status(500).json({
        error: 'Failed to process log entry',
        message: error.message
      });
    }
  })
);

/**
 * POST /api/v1.0.0/logs/batch
 * Accept multiple log entries in a single request for efficiency
 */
router.post('/api/v1.0.0/logs/batch',
  celebrate({
    body: Joi.object({
      logs: Joi.array().items(
        Joi.object({
          level: Joi.string().valid('error', 'warn', 'info', 'debug').required(),
          message: Joi.string().required(),
          metadata: Joi.object().optional(),
          source: Joi.string().default('admin-panel'),
          timestamp: Joi.date().optional(),
          userId: Joi.number().integer().optional(),
          clientId: Joi.number().integer().optional(),
          sessionId: Joi.string().optional(),
          userAgent: Joi.string().optional(),
          url: Joi.string().optional(),
          action: Joi.string().optional(),
          category: Joi.string().optional()
        })
      ).min(1).max(100).required() // Limit batch size to prevent abuse
    })
  }),
  asyncRouteWithErrorLogging(async (req, res) => {
    try {
      const { logs } = req.body;
      const processedLogs = [];

      // Extract common request information
      const authUser = req.auth?.user;
      const commonMetadata = {
        requestId: req.headers['x-request-id'],
        ip: req.ip || req.connection.remoteAddress,
        origin: req.headers.origin,
        batchTimestamp: new Date().toISOString()
      };

      // Process each log entry
      for (const logEntry of logs) {
        const {
          level,
          message,
          metadata = {},
          source,
          timestamp,
          userId,
          clientId,
          sessionId,
          userAgent,
          url,
          action,
          category
        } = logEntry;

        const finalUserId = userId || authUser?.id;
        const finalClientId = clientId || authUser?.client?.id;

        const logMetadata = {
          source,
          userId: finalUserId,
          clientId: finalClientId,
          sessionId: sessionId || req.auth?.session?.id,
          userAgent: userAgent || req.headers['user-agent'],
          url: url || req.originalUrl,
          action,
          category,
          timestamp: timestamp || new Date().toISOString(),
          originalMetadata: metadata,
          ...commonMetadata
        };

        // Log the message
        logger.log(level, `[${source}] ${message}`, logMetadata);

        processedLogs.push({
          level,
          message: message.substring(0, 100), // Truncate for response
          timestamp: logMetadata.timestamp,
          source
        });
      }

      res.status(200).json({
        message: `Successfully processed ${logs.length} log entries`,
        processedCount: logs.length,
        logs: processedLogs
      });

    } catch (error) {
      logger.error('Error processing batch log request', {
        error: error.message,
        stack: error.stack,
        logCount: req.body?.logs?.length || 0
      });

      res.status(500).json({
        error: 'Failed to process batch log entries',
        message: error.message
      });
    }
  })
);

export default {
  router: router,
  securityConstraints: [
    {
      regex: '/api/v.*/logs',
      methods: ['POST'],
      accessLevels: ['admin', 'manager', 'super admin', 'user'] // Allow users to log from admin panel
    }
  ]
};
