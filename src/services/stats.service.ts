import { ApiService } from './api.service';
import * as interfaces from '../interfaces';
import { countType, filterToDateRange, getCsv, numberWithCommas } from 'utils/utils';
import { FilterType } from 'hooks/zustand/filterStore';
import moment from 'moment';

export class StatService extends ApiService {
    id: string
    stats: interfaces.StatsInterface;
    analytics: interfaces.StatEventInterface[];
    computedValues: interfaces.ComputedAnalyticsInterface;
    userAccessLevel: string;

    // Engagement
    questionAnalytics: interfaces.CategoryAnalyticsInterface[];
    trendingQuestionAnalytics: interfaces.TrendingCategoryAnalyticsInterface[];
    mobileDesktop: interfaces.EngagementAnalyticsInterface['mobileDesktop'][];
    referralPercentage: interfaces.EngagementAnalyticsInterface['referralPercentage'][];
    engagementsChart: interfaces.ChartDataInterface[];
    sentimentAnalytics: interfaces.SentimentAnalyticsInterface[];
    aiQueriesChart: interfaces.ChartDataInterface[];
    aiQueriesSentiments: interfaces.SentimentAnalyticsInterface[];
    transcripts: interfaces.TranscriptInterface[];
    videoPlayStats: interfaces.VideoPlayStats[];
    videos: interfaces.VideoStatInterface[];

    // Email
    emailAnalytics: interfaces.EmailAnalyticsInterface;

    public chartColors = {
        red: 'rgb( 255, 99, 132 )',
        orange: 'rgb( 255, 159, 64 )',
        yellow: 'rgb( 255, 205, 86 )',
        green: 'rgb( 41, 179, 110 )',
        blue: 'rgb( 52, 83, 141 )',
        purple: 'rgb( 153, 102, 255 )',
        grey: 'rgb( 201, 203, 207 )'
    }
    public chartOptions = {
        responsive: true,
        title: { display: false, },
        legend: { display: false },
        tooltips: { mode: 'index', intersect: false },
        scales: {
            y: {
                stacked: true,
                ticks: {
                    maxTicksLimit: 5, stepSize: 5, padding: 20, color: this.chartColors.blue,
                    font: { size: 16, weight: 'bold' }, lineHeight: 3
                }
            },
            x: {
                borderColor: this.chartColors.grey,
                ticks: {
                    maxTicksLimit: 7, color: this.chartColors.blue,
                    font: { size: 16 }, lineHeight: 3
                }
            }
        }
    };

    constructor(token?: string, clientId?: string, userAccessLevel?: string) {
        super(token, clientId);
        this.id = 'Stats';
        this.userAccessLevel = userAccessLevel || 'admin';

        this.stats = {
            categories: [],
            locations: [],
            questions: null,
            votes: null,
            engagement: [],
            videos: [],
            emails: []
        }

        this.analytics = [];
        this.computedValues = {
            trendingData: {
                questions: [],
                queries: []
            },
            totalEngagements: null,
            totalTraffic: null,
            engagementRate: null,
            timeSaved: null,
            totalVideoViews: 0,
            averageWatchTime: "0:00",
            completedWatchRate: "0",
            deliveredEmails: 0,
            openedEmails: 0,
            clickedEmails: 0,
            openedEmailsPercentage: 0,
            clickedEmailsPercentage: 0,
            totalQueries: 0,
            trendingQueries: 0,
            trendingQuestions: 0,
            topCategories: [],
            bulkSendData: [],
            sentimentData: [],
            aiQueriesSentimentData: [],
            quartileData: [],
            completedWatchRates: {}
        }

        this.questionAnalytics = [];
        this.trendingQuestionAnalytics = [];
        this.mobileDesktop = [];
        this.referralPercentage = [];
        this.engagementsChart = [];
        this.sentimentAnalytics = [];
        this.aiQueriesChart = [];
        this.aiQueriesSentiments = [];
        this.transcripts = [];
        this.videoPlayStats = [];
        this.videos = [];

        this.emailAnalytics = {
            opens: 0,
            clicks: 0,
            deliveries: 0,
            chartData: [],
            bulkSendData: {
                bulkSends: [],
                videoPlayStats: []
            }
        };

    }

    async fetch(callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.stats}?clientId=${this.clientId}`;
        const errorIdentifier = `${this.id} -> Get`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.StatsInterface = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.stats = responseData;

            if (callback) callback(this.stats);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    // Voter analytics
    async fetchAnalytics(timeFilter: FilterType | null, locationFilter: FilterType[], isSuper: boolean, callback?: VoidFunction | any) {
        const formattedTimeFilter = filterToDateRange(timeFilter?.value, this.userAccessLevel)
        const formattedLocationFilters = JSON.stringify(locationFilter.map((filter: FilterType) => filter.value));
        const endpoint = `${this.base}${this.paths.analytics}?clientId=${isSuper ? null : this.clientId}&start=${formattedTimeFilter.start}&end=${formattedTimeFilter.end}&location=${formattedLocationFilters}`;
        const errorIdentifier = `${this.id} -> Get`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.EngagementAnalyticsInterface = response.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.analytics = responseData.analytics;
            this.mobileDesktop = responseData.mobileDesktop;
            this.referralPercentage = responseData.referralPercentage;
            this.engagementsChart = responseData.engagementsChart;
            this.sentimentAnalytics = responseData.sentiments;
            this.aiQueriesChart = responseData.aiQueriesChart;
            this.aiQueriesSentiments = responseData.aiQueriesSentiments;

            // Note: computeValues() is now called externally to prevent race conditions

            if (callback) callback(responseData);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    // Voter analytics - Queries
    async fetchQuestionAnalytics(timeFilter: FilterType | null, locationFilter: FilterType[], isSuper: boolean, callback?: VoidFunction | any) {
        const formattedTimeFilter = filterToDateRange(timeFilter?.value, this.userAccessLevel)
        const formattedLocationFilters = JSON.stringify(locationFilter.map((filter: FilterType) => filter.value));
        const endpoint = `${this.base}${this.paths.questionAnalytics}?clientId=${isSuper ? null : this.clientId}&start=${formattedTimeFilter.start}&end=${formattedTimeFilter.end}&location=${formattedLocationFilters}`;
        const errorIdentifier = `${this.id} -> Get`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.CategoryAnalyticsInterface[] = response.data.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.questionAnalytics = responseData;
            // Note: computeQuestionValues() is now called externally to prevent race conditions
            if (callback) callback(this.questionAnalytics);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    // Voter analytics - Trending
    async fetchTrendingAnalytics(timeFilter: FilterType | null, locationFilter: FilterType[], isSuper: boolean, callback?: VoidFunction | any) {
        const formattedTimeFilter = filterToDateRange(timeFilter?.value, this.userAccessLevel)
        const formattedLocationFilters = JSON.stringify(locationFilter.map((filter: FilterType) => filter.value));
        const endpoint = `${this.base}${this.paths.trendingAnalytics}?clientId=${isSuper ? null : this.clientId}&start=${formattedTimeFilter.start}&end=${formattedTimeFilter.end}&location=${formattedLocationFilters}`;
        const errorIdentifier = `${this.id} -> Get`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.TrendingCategoryAnalyticsInterface[] = response.data.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.trendingQuestionAnalytics = responseData;
            // Note: computeTrendingValues() is now called externally to prevent race conditions
            if (callback) callback(this.trendingQuestionAnalytics);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    // Emails
    async fetchEmailAnalytics(timeFilter: FilterType | null, locationFilter: FilterType[], isSuper: boolean, callback?: VoidFunction | any) {
        const formattedTimeFilter = filterToDateRange(timeFilter?.value, this.userAccessLevel)
        const formattedLocationFilters = JSON.stringify(locationFilter.map((filter: FilterType) => filter.value));
        const endpoint = `${this.base}${this.paths.emailAnalytics}?clientId=${isSuper ? null : this.clientId}&start=${formattedTimeFilter.start}&end=${formattedTimeFilter.end}&location=${formattedLocationFilters}`;
        const errorIdentifier = `${this.id} -> Get`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.EmailAnalyticsInterface = response.data.data[0];

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.emailAnalytics = {
                ...this.emailAnalytics,
                opens: Number(responseData.opens),
                clicks: Number(responseData.clicks),
                deliveries: Number(responseData.deliveries)
            };
            this.computeEmailValues();
            if (callback) callback(this.emailAnalytics);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    // Emails - Chart
    async fetchEmailChartData(timeFilter: FilterType | null, locationFilter: FilterType[], isSuper: boolean) {
        const formattedTimeFilter = filterToDateRange(timeFilter?.value, this.userAccessLevel)
        const formattedLocationFilters = JSON.stringify(locationFilter.map((filter: FilterType) => filter.value));
        const endpoint = `${this.base}${this.paths.emailChartData}?clientId=${isSuper ? null : this.clientId}&start=${formattedTimeFilter.start}&end=${formattedTimeFilter.end}&location=${formattedLocationFilters}`;
        const errorIdentifier = `${this.id} -> Get`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.ChartDataInterface[] = response.data.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.emailAnalytics.chartData = responseData;
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    // Emails - Bulk Sends
    async fetchEmailBulkSendData(timeFilter: FilterType | null, locationFilter: FilterType[], isSuper: boolean, callback?: VoidFunction | any) {
        const formattedTimeFilter = filterToDateRange(timeFilter?.value, this.userAccessLevel)
        const formattedLocationFilters = JSON.stringify(locationFilter.map((filter: FilterType) => filter.value));
        const endpoint = `${this.base}${this.paths.emailBulkSends}?clientId=${isSuper ? null : this.clientId}&start=${formattedTimeFilter.start}&end=${formattedTimeFilter.end}&location=${formattedLocationFilters}`;
        const errorIdentifier = `${this.id} -> Get`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.EmailAnalyticsInterface['bulkSendData'] = response.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.emailAnalytics.bulkSendData = responseData;
            this.videoPlayStats = responseData.videoPlayStats;
            this.computeQuartileData();

            this.computeEmailBulkSendData();
            if (callback) callback(this.computedValues.bulkSendData);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    async getVideos(timeFilter: FilterType | null, locationFilter: FilterType[], isSuper: boolean, callback?: VoidFunction | any) {
        const formattedTimeFilter = filterToDateRange(timeFilter?.value, this.userAccessLevel);
        const formattedLocationFilters = JSON.stringify(locationFilter.map((filter: FilterType) => filter.value));
        const endpoint = `${this.base}${this.paths.videoStats}?clientId=${isSuper ? null : this.clientId}&location=${formattedLocationFilters}&start=${formattedTimeFilter.start}&end=${formattedTimeFilter.end}`;
        const errorIdentifier = `${this.id} -> Get Videos`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: { videos: interfaces.VideoStatInterface[], videoPlayStats: interfaces.VideoPlayStats[] } = response.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.videos = responseData.videos;
            this.videoPlayStats = responseData.videoPlayStats;
            this.computeQuartileData();

            if (callback) callback(responseData);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    async getTranscripts(isSuper: boolean, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.transcripts}?clientId=${isSuper ? null : this.clientId}`;
        const errorIdentifier = `${this.id} -> Get Transcripts`;

        return await this.axiosInstance.get(endpoint).then((response: any) => {
            const responseData: interfaces.TranscriptInterface[] = response.data.data;

            if (!responseData || response.errors)
                return this.reportInvalidResponseError(errorIdentifier, response);

            this.transcripts = responseData;
            if (callback) callback(responseData);
        }).catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    async postTranscript(values: { clientId: number, name: string, text: string }, callback?: VoidFunction | any) {
        const endpoint = `${this.base}${this.paths.transcripts}`;
        const errorIdentifier = `${this.id} -> Post Transcript`;

        return await this.axiosInstance.post(endpoint, values).then().catch((error: any) => {
            this.reportRequestError(errorIdentifier, error);
        });
    }

    // Anything that needs to be calculated
    computeValues() {
        // Ensure analytics is an array to prevent errors
        if (!this.analytics || !Array.isArray(this.analytics)) {
            this.analytics = [];
        }

        const pageViewEvents = countType(this.analytics, "event_category", "PageView") + countType(this.analytics, "event_category", "Auth");
        const aiQueryEvents = countType(this.analytics, "event_category", "search_query");
        const setVideoEvents = countType(this.analytics, "event_action", "setVideo");
        const playVideoEvents = countType(this.analytics, "event_action", "playVideo");
        const pauseVideoEvents = countType(this.analytics, "event_action", "pauseVideo");
        const sendQuestionEvents = countType(this.analytics, "event_action", "sendQuestion");
        const aiQueries = this.analytics.filter((event: any) => event.event_category === "search_query");
        const questions = this.analytics.filter((event: any) => event.event_category === "sendQuestion");
        // Calculate inferred module_open events - one per user session that lacks a module_open (embed.repd.us only)
        const inferredEventsCount = this.calculateInferredEvents();
        this.computedValues.totalEngagements = this.analytics.length + inferredEventsCount;
        this.computedValues.totalTraffic = this.analytics.filter((item: any) => item.event_category === 'PageView' || item.event_action === 'module_open')?.length + inferredEventsCount;

        const sentimentToPercent = (sentimentData: interfaces.SentimentAnalyticsInterface[]) => {
            if (!sentimentData || !Array.isArray(sentimentData)) {
                return [];
            }
            return sentimentData.map((sentiment: interfaces.SentimentAnalyticsInterface) => {
                const happy = Number(sentiment.happy);
                const neutral = Number(sentiment.neutral);
                const unhappy = Number(sentiment.unhappy);

                const happyPercentage = Math.round((happy / (happy + neutral + unhappy)) * 100);
                const neutralPercentage = Math.round((neutral / (happy + neutral + unhappy)) * 100);
                const unhappyPercentage = Math.round((unhappy / (happy + neutral + unhappy)) * 100);

                return {
                    date: moment(sentiment.date).format("YYYY-MM-DD"),
                    happyPercentage: isNaN(happyPercentage) ? 0 : happyPercentage,
                    neutralPercentage: isNaN(neutralPercentage) ? 0 : neutralPercentage,
                    unhappyPercentage: isNaN(unhappyPercentage) ? 0 : unhappyPercentage
                }
            });
        }

        this.computedValues.sentimentData = sentimentToPercent(this.sentimentAnalytics);
        this.computedValues.aiQueriesSentimentData = sentimentToPercent(this.aiQueriesSentiments);
        // % of events that are not PageViews
        const engagementRate = Math.round(
            (
                (this.computedValues.totalEngagements / this.computedValues.totalTraffic)
            ) * 100
        );

        this.computedValues.engagementRate = engagementRate || 0;
        this.computedValues.totalVideoViews = setVideoEvents + playVideoEvents + pauseVideoEvents;


        const videoStatEvents = this.analytics.filter((event: any) => ["playVideo", "pauseVideo", "setVideo"].includes(event.event_action));
        const videoSetAndPlayCount = videoStatEvents.filter((event: any) => ["playVideo", "setVideo"].includes(event.event_action)).length;

        const videoPlaysByUser = videoStatEvents.reduce((acc: any, event: any) => {
            if (!acc[event?.user_id]) {
                acc[event?.user_id] = {
                    events: [],
                    video_duration: event.video_duration,
                };
            }
            acc[event?.user_id].events?.push(event);
            return acc;
        }, {});

        let totalWatchTime = [];
        let watchedToCompletionCount = 0;
        // For tracking per-video completion rates
        const videoWatchCounts: { [videoId: string]: { total: number, completed: number } } = {};

        for (const userId in videoPlaysByUser) {
            let prevEvent: any = {};

            if (!videoPlaysByUser[userId]) continue;

            for (const event of videoPlaysByUser[userId].events) {
                const videoId = event.answer_id || event.video_id;

                if (!videoId) continue;

                // Initialize video tracking if not exists
                if (!videoWatchCounts[videoId]) {
                    videoWatchCounts[videoId] = { total: 0, completed: 0 };
                }

                if (!event.video_duration) {
                    event.video_duration = 60;
                }
                if (["playVideo", "setVideo"].includes(event.event_action)) {
                    totalWatchTime.push(event.video_duration);
                    watchedToCompletionCount++;

                    // Track per video
                    videoWatchCounts[videoId].total++;
                    videoWatchCounts[videoId].completed++;
                } else if (event.event_action === "pauseVideo") {
                    if (["playVideo", "setVideo"].includes(prevEvent.event_action)) {
                        const timeDiff = moment(event.created_at).diff(prevEvent.created_at, "s");
                        if (timeDiff < event.video_duration && timeDiff >= 0) {
                            totalWatchTime[totalWatchTime.length - 1] = timeDiff;
                        }
                        watchedToCompletionCount--;

                        // Track per video (decrement completion count if paused before end)
                        const prevVideoId = prevEvent.answer_id || prevEvent.video_id;
                        if (prevVideoId && videoWatchCounts[prevVideoId]) {
                            videoWatchCounts[prevVideoId].completed--;
                        }
                    }
                }
                prevEvent = event;
            }
        }

        const completedWatchRate = ((watchedToCompletionCount / videoSetAndPlayCount) * 100)?.toFixed(1);
        const averageWatchTime = totalWatchTime.reduce((acc, cur) => acc + cur, 0) / totalWatchTime.length;
        this.computedValues.averageWatchTime = moment.utc(averageWatchTime * 1000).format('mm:ss');

        this.computedValues.completedWatchRate = isNaN(Number(completedWatchRate)) ? "0" : completedWatchRate;

        // Calculate per-video completed watch rates
        const completedWatchRates: { [videoId: string]: string } = {};
        for (const videoId in videoWatchCounts) {
            const { total, completed } = videoWatchCounts[videoId];
            if (total > 0) {
                const rate = ((completed / total) * 100).toFixed(1);
                // completedWatchRates[videoId]
                completedWatchRates[0] = isNaN(Number(rate)) ? "0" : Number(rate) < 0 ? "0" : rate;
            } else {
                completedWatchRates[0] = "0";
            }
        }

        this.computedValues.completedWatchRates = completedWatchRates;

        this.computedValues.trendingQueries = aiQueries
            .filter((query: any) => {
                const sixMonthsAgo = new Date();
                sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
                return new Date(query.created_at) > sixMonthsAgo;
            }).length;

        this.computedValues.trendingQuestions = questions
            .filter((query: any) => {
                const sixMonthsAgo = new Date();
                sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
                return new Date(query.created_at) > sixMonthsAgo;
            }).length;

        // Note: Zustand store update is now handled externally to prevent race conditions
    }

    // Update Zustand store with current computed values
    public updateStatsStore() {
        // Import the store dynamically to avoid circular dependencies
        import('hooks/zustand/statsStore').then(({ useStatsStore }) => {
            const { updateComputedValues } = useStatsStore.getState();
            updateComputedValues(this.computedValues);
        });
    }

    computeQuestionValues() {
        const totalQuestions = this.questionAnalytics.reduce((sum, category) => sum + Number(category.current_period_count), 0);
        const topCategories = this.questionAnalytics?.map((category: interfaces.CategoryAnalyticsInterface) => {
            const volumeDiffPercentage = Math.round(
                (
                    (category.current_period_count - category.previous_period_count)
                    / category.previous_period_count
                ) * 100
            );

            const volumePercentage = Math.round((category.current_period_count / totalQuestions) * 100)

            return {
                category: category.category,
                sentiment: category.sentiment,
                count: category.current_period_count,
                questions: category.questions?.map(q => ({
                    ...q,
                    id: String(q.id) // Convert number id to string
                })),
                volumePercentage: volumePercentage,
                volumeDiffPercentage: volumeDiffPercentage === Infinity ? 0 : volumeDiffPercentage
            }
        });
        topCategories.sort((a, b) => b.count - a.count);
        this.computedValues.topCategories = topCategories.slice(0, 12);

        // Note: Zustand store update is now handled externally to prevent race conditions
    }

    computeTrendingValues() {
        this.computedValues.trendingData = {
            questions: this.trendingQuestionAnalytics.filter((question: interfaces.TrendingCategoryAnalyticsInterface) => question.source === "questions").map((question: interfaces.TrendingCategoryAnalyticsInterface) => {
                return {
                    category: question.category ? question.category : "Other",
                    sentiment: question.average_sentiment,
                    question_count: Number(question.question_count)
                }
            }),
            queries: this.trendingQuestionAnalytics.filter((query: interfaces.TrendingCategoryAnalyticsInterface) => query.source === "ai_queries").map((query: interfaces.TrendingCategoryAnalyticsInterface) => {
                return {
                    category: query.category ? query.category : "Other",
                    sentiment: query.average_sentiment,
                    question_count: Number(query.question_count)
                }
            })
        }
        const totalQueries = this.trendingQuestionAnalytics?.reduce((sum, q) => sum + Number(q.question_count), 0);
        this.computedValues.totalQueries = totalQueries;
        const totalAiQueries = this.trendingQuestionAnalytics?.filter((query: interfaces.TrendingCategoryAnalyticsInterface) => query.source === "ai_queries").reduce((sum, q) => sum + Number(q.question_count), 0);
        this.computedValues.timeSaved = moment.duration({ "minutes": totalAiQueries * 5 }).asHours().toFixed(0);

        // Note: Zustand store update is now handled externally to prevent race conditions
    }

    computeEmailValues() {
        this.computedValues.deliveredEmails = this.emailAnalytics.deliveries;
        const openedEmailsPercentage = this.emailAnalytics.opens / this.emailAnalytics.deliveries * 100;
        this.computedValues.openedEmailsPercentage = isNaN(openedEmailsPercentage) || openedEmailsPercentage > 100 ? 100 : openedEmailsPercentage;
        const clickedEmailsPercentage = this.emailAnalytics.clicks / this.emailAnalytics.deliveries * 100;
        this.computedValues.clickedEmailsPercentage = isNaN(clickedEmailsPercentage) || clickedEmailsPercentage > 100 ? 100 : clickedEmailsPercentage;
        this.computedValues.openedEmails = this.emailAnalytics.opens;
        this.computedValues.clickedEmails = this.emailAnalytics.clicks;

        // Note: Zustand store update is now handled externally to prevent race conditions
    }

    computeEmailBulkSendData(): void {
        const computedData = this.emailAnalytics.bulkSendData.bulkSends.map((send: interfaces.BulkSendInterface): interfaces.BulkSendComputedInterface => {
            // Only keeping subject and date data as that's all that's used on the Emails page
            return {
                ...send,
                date: moment(send.created_at).format("MMM Do, YYYY"),
            };
        });

        // Commented out calculations that are no longer needed:
        /*
            // Convert string values to numbers for calculations
            const userCount: number = Number(send.user_count) || 0;
            const delivered: number = Number(send.delivered) || 0;
            const opens: number = Number(send.opens) || 0;
            const clicks: number = Number(send.clicks) || 0;
            const unsubscribes: number = Number(send.unsubscribes) || 0;
            const blocks: number = Number(send.blocks) || 0;

            // Calculate percentages safely
            const deliveredPercentage: number = userCount > 0 ? (delivered / userCount * 100) : 0;
            const openedPercentage: number = delivered > 0 ? (opens / delivered * 100) : 0;
            const clickedPercentage: number = delivered > 0 ? (clicks / delivered * 100) : 0;
            const unsubscribePercentage: number = delivered > 0 ? (unsubscribes / delivered * 100) : 0;
            const blocksPercentage: number = delivered > 0 ? (blocks / delivered * 100) : 0;
            const avgOpenRate: number = Number(send.avgOpenRate) || 0;

            // Cap percentages between 0 and 100
            const deliveredPercentageRounded: number = Math.round(Math.min(Math.max(0, deliveredPercentage), 100));
            const openedPercentageRounded: number = Math.round(Math.min(Math.max(0, openedPercentage), 100));
            const clickedPercentageRounded: number = Math.round(Math.min(Math.max(0, clickedPercentage), 100));
            const unsubscribePercentageRounded: number = Math.round(Math.min(Math.max(0, unsubscribePercentage), 100));
            const blocksPercentageRounded: number = Math.round(Math.min(Math.max(0, blocksPercentage), 100));
            const avgOpensPercentageRounded: number = Math.round(Math.min(Math.max(0, avgOpenRate), 100));

            return {
                ...send,
                opens: opens,
                clicks: clicks,
                unsubscribes: unsubscribes,
                blocks: blocks,
                delivered: delivered,
                user_count: userCount,
                opensString: `${numberWithCommas(opens)} (${openedPercentageRounded}%)`,
                clicksString: `${numberWithCommas(clicks)} (${clickedPercentageRounded}%)`,
                unsubscribesString: `${unsubscribes} (${unsubscribePercentageRounded}%)`,
                date: moment(send.created_at).format("MMM Do, YYYY"),
                deliveredPercentage: deliveredPercentageRounded,
                opensPercentage: openedPercentageRounded,
                clicksPercentage: clickedPercentageRounded,
                unsubscribesPercentage: unsubscribePercentageRounded,
                avgOpensPercentage: avgOpensPercentageRounded,
                blocksPercentage: blocksPercentageRounded,
            };
        */

        // Simplified: No merging needed since we only need subject and date
        const mergedData: interfaces.BulkSendComputedInterface[] = computedData;

        // Commented out merging logic that's no longer needed:
        /*
        // Merge data by grouping similar sends together
        const mergedData: interfaces.BulkSendComputedInterface[] = computedData.reduce(
            (acc: interfaces.BulkSendComputedInterface[], current: interfaces.BulkSendComputedInterface): interfaces.BulkSendComputedInterface[] => {
                // Find matching sends based on name, date, and video - but not necessarily subject
                const duplicate = acc.find(
                    (item: interfaces.BulkSendComputedInterface): boolean =>
                        item.saved_list_name === current.saved_list_name &&
                        moment(item.created_at).format("YYYY-MM-DD") === moment(current.created_at).format("YYYY-MM-DD") &&
                        item.videoUrl === current.videoUrl
                );

                if (duplicate) {
                    // Merge the stats from duplicate entries
                    duplicate.opens += current.opens;
                    duplicate.clicks += current.clicks;
                    duplicate.unsubscribes += current.unsubscribes;
                    duplicate.blocks += current.blocks;
                    duplicate.delivered += current.delivered;

                    // Recalculate percentages after merging
                    duplicate.opensPercentage = duplicate.delivered > 0 ?
                        Math.round(Math.min((duplicate.opens / duplicate.delivered * 100), 100)) : 0;
                    duplicate.clicksPercentage = duplicate.delivered > 0 ?
                        Math.round(Math.min((duplicate.clicks / duplicate.delivered * 100), 100)) : 0;
                    duplicate.unsubscribesPercentage = duplicate.delivered > 0 ?
                        Math.round(Math.min((duplicate.unsubscribes / duplicate.delivered * 100), 100)) : 0;
                    duplicate.blocksPercentage = duplicate.delivered > 0 ?
                        Math.round(Math.min((duplicate.blocks / duplicate.delivered * 100), 100)) : 0;

                    // Update the display strings
                    duplicate.opensString = `${numberWithCommas(duplicate.opens)} (${duplicate.opensPercentage}%)`;
                    duplicate.clicksString = `${numberWithCommas(duplicate.clicks)} (${duplicate.clicksPercentage}%)`;
                    duplicate.unsubscribesString = `${duplicate.unsubscribes} (${duplicate.unsubscribesPercentage}%)`;
                } else {
                    acc.push(current);
                }

                return acc;
            }, []
        );
        */

        // Sort results by date (most recent first)
        mergedData.sort((a: interfaces.BulkSendComputedInterface, b: interfaces.BulkSendComputedInterface): number =>
            moment(b.created_at).valueOf() - moment(a.created_at).valueOf()
        );

        this.computedValues.bulkSendData = mergedData;
    }

    /**
     * Calculate inferred module_open events - one per user session that doesn't already have a module_open
     * A session is defined as a 5-minute window of user activity
     * @param events Optional array of events to use instead of this.analytics
     */
    calculateInferredEvents(events?: any[]): number {
        const eventsToUse = events || this.analytics;
        if (!eventsToUse || eventsToUse.length === 0) {
            return 0;
        }

        let inferredCount = 0;

        // Filter events to only include those from embed.repd.us host
        const embedEvents = eventsToUse.filter((event: any) =>
            event.host && event.host.includes('embed.repd.us')
        );

        // Group events by user_id and sort by timestamp
        const eventsByUser = embedEvents.reduce((acc: any, event: any) => {
            const userId = event.user_id;
            if (!userId || !event.created_at) return acc;

            if (!acc[userId]) {
                acc[userId] = [];
            }
            acc[userId].push(event);
            return acc;
        }, {});

        // Process each user's events to identify sessions
        for (const userId in eventsByUser) {
            const userEvents = eventsByUser[userId];

            // Sort events by timestamp
            userEvents.sort((a: any, b: any) => moment(a.created_at).valueOf() - moment(b.created_at).valueOf());

            // Group events into sessions (5-minute windows)
            const sessions: any[][] = [];
            let currentSession: any[] = [];
            let sessionStartTime: moment.Moment | null = null;

            for (const event of userEvents) {
                const eventTime = moment(event.created_at);

                if (!sessionStartTime || eventTime.diff(sessionStartTime, 'minutes') > 5) {
                    // Start a new session
                    if (currentSession.length > 0) {
                        sessions.push(currentSession);
                    }
                    currentSession = [event];
                    sessionStartTime = eventTime;
                } else {
                    // Add to current session
                    currentSession.push(event);
                }
            }

            // Don't forget the last session
            if (currentSession.length > 0) {
                sessions.push(currentSession);
            }

            // Check each session for module_open events
            for (const session of sessions) {
                const hasModuleOpen = session.some((event: any) => event.event_action === 'module_open');

                // If session has no module_open event, we need to infer one
                if (!hasModuleOpen) {
                    inferredCount++;
                }
            }
        }

        return inferredCount;
    }

    computeQuartileData() {
        const playCounts = this.videoPlayStats
            .filter(row => ["setVideo", "playVideo"].includes(row.event_action))
            .reduce((acc, row) => {
                acc.set(row.answer_id, (acc.get(row.answer_id) || 0) + 1);
                return acc;
            }, new Map());

        // Initialize quartile counts
        const quartileCounts = new Map();
        playCounts.forEach((_, video) => {
            quartileCounts.set(video, [0, 0, 0, 0]);
        });

        // Process each user's interactions with each video
        const groupedByUserVideo = this.videoPlayStats.reduce((acc: any, row: interfaces.VideoPlayStats) => {
            const key = `${row.user}_${row.answer_id}`;
            if (!acc[key]) acc[key] = [];
            acc[key].push(row);
            return acc;
        }, {});

        // @ts-ignore
        Object.entries(groupedByUserVideo).forEach(([key, videoRows]: [string, interfaces.VideoPlayStats[]]) => {
            const video = videoRows[0].answer_id;
            const videoDuration = videoRows[0].video_duration; // Assume duration is constant
            let startTime: Date | null = null;

            videoRows.forEach(row => {
                if (["setVideo", "playVideo"].includes(row.event_action)) {
                    startTime = new Date(row.created_at);
                } else if (row.event_action === "pauseVideo" && startTime) {
                    // @ts-ignore
                    const timeWatched = (new Date(row.created_at) - startTime) / 1000;
                    const quartileRatio = timeWatched / videoDuration;
                    const quartiles = quartileCounts.get(video);

                    if (quartileRatio <= 0.25) quartiles[0]++;
                    else if (quartileRatio <= 0.5) quartiles[1]++;
                    else if (quartileRatio <= 0.75) quartiles[2]++;
                    else quartiles[3]++;

                    startTime = null; // Reset for next play event
                }
            });

            // Assume full watch if play event wasn't paused
            if (startTime) {
                quartileCounts.get(video)[3]++;
            }
        });

        // Convert quartile counts to an array for tabular representation
        const resultArray = Array.from(quartileCounts.entries()).map(([video, quartiles]) => {
            const totalPlays = quartiles.reduce((sum: number, val: number) => sum + val, 0);
            return {
                answer_id: video,
                "0-25": Math.round(quartiles[0] / totalPlays * 100),
                "25-50": Math.round(quartiles[1] / totalPlays * 100),
                "50-75": Math.round(quartiles[2] / totalPlays * 100),
                "75-100": Math.round(quartiles[3] / totalPlays * 100),
                "totalPlays": totalPlays,
                "expectedPlays": playCounts.get(video) || 0
            };
        });

        this.computedValues.quartileData = resultArray;
    }

    getEngagementsCsv() {
        const data = [];
        const sanitizedTopCategories = [...this.computedValues.topCategories]?.map((category: any) => {
            return {
                category: category.category,
                sentiment: category.sentiment,
                count: category.count,
                volumePercentage: category.volumePercentage,
                volumeDiffPercentage: category.volumeDiffPercentage,
                // questions: category.questions.length
            }
        });
        data.push(
            {
                totalTraffic: this.computedValues.totalTraffic,
                engagementRate: this.computedValues.engagementRate,
                timeSaved: this.computedValues.timeSaved,
                totalQueries: this.computedValues.totalQueries,
            }
        );
        if (this.computedValues.topCategories?.length) {
            data.push(
                sanitizedTopCategories
            )
        }
        if (this.computedValues.trendingData) {
            data.push(
                [...this.computedValues.trendingData.queries, ...this.computedValues.trendingData.questions]
            )
        }
        return getCsv(data);
    }

    getVideosCsv() {
        const data = [];
        data.push(
            {
                totalVideoViews: this.computedValues.totalVideoViews,
                averageWatchTime: this.computedValues.averageWatchTime,
                completedWatchRate: this.computedValues.completedWatchRate,
            }
        );
        return getCsv(data);
    }

    getEmailsCsv() {
        const data = [];

        data.push(
            {
                deliveredEmails: this.computedValues.deliveredEmails,
                openedEmails: this.computedValues.openedEmails,
                clickedEmails: this.computedValues.clickedEmails,
                openedEmailsPercentage: this.computedValues.openedEmailsPercentage,
                clickedEmailsPercentage: this.computedValues.clickedEmailsPercentage,
            }
        );

        if (this.computedValues.bulkSendData?.length) {
            data.push(
                [...this.computedValues.bulkSendData]
            )
        }

        return getCsv(data);
    }

    /**
     * Calculate video watch statistics for a set of events
     * @param events Array of video-related events
     * @returns Object with averageWatchTime and completedWatchRate
     */
    calculateVideoStats(events: any[]) {
        const videoStatEvents = events.filter((event: any) => ["playVideo", "pauseVideo", "setVideo"].includes(event.event_action));

        if (videoStatEvents.length === 0) {
            return {
                averageWatchTime: "0:00",
                completedWatchRate: "0"
            };
        }

        const videoPlaysByUser = videoStatEvents.reduce((acc: any, event: any) => {
            if (!acc[event?.user_id]) {
                acc[event?.user_id] = {
                    events: [],
                    video_duration: event.video_duration,
                };
            }
            acc[event?.user_id].events?.push(event);
            return acc;
        }, {});

        let totalWatchTime = 0;
        let completedWatches = 0;
        let totalWatches = 0;

        for (const userId in videoPlaysByUser) {
            const userVideoData = videoPlaysByUser[userId];
            const userEvents = userVideoData.events;
            const videoDuration = userVideoData.video_duration || 0;

            if (userEvents.length > 0 && videoDuration > 0) {
                totalWatches++;

                // Calculate watch time based on play/pause patterns
                let userWatchTime = 0;
                let isPlaying = false;
                let lastPlayTime = 0;

                // Sort events by timestamp
                const sortedEvents = userEvents.sort((a: any, b: any) =>
                    moment(a.created_at).valueOf() - moment(b.created_at).valueOf()
                );

                for (const event of sortedEvents) {
                    const eventTime = moment(event.created_at).valueOf();

                    if (event.event_action === 'playVideo' || event.event_action === 'setVideo') {
                        if (!isPlaying) {
                            isPlaying = true;
                            lastPlayTime = eventTime;
                        }
                    } else if (event.event_action === 'pauseVideo') {
                        if (isPlaying) {
                            userWatchTime += Math.min((eventTime - lastPlayTime) / 1000, videoDuration);
                            isPlaying = false;
                        }
                    }
                }

                // If still playing at the end, estimate remaining watch time
                if (isPlaying) {
                    const estimatedRemainingTime = Math.min(videoDuration * 0.1, videoDuration - userWatchTime);
                    userWatchTime += estimatedRemainingTime;
                }

                totalWatchTime += userWatchTime;

                // Check if completed (watched at least 80% of video)
                if (userWatchTime >= videoDuration * 0.8) {
                    completedWatches++;
                }
            }
        }

        const averageWatchTime = totalWatches > 0 ?
            moment.utc(moment.duration(totalWatchTime / totalWatches, 'seconds').asMilliseconds()).format('m:ss') : "0:00";
        const completedWatchRate = totalWatches > 0 ?
            ((completedWatches / totalWatches) * 100).toFixed(0) : "0";

        return {
            averageWatchTime,
            completedWatchRate
        };
    }

    computeStatsForHostType(events: any[], eventSource: string) {
        if (!events || events.length === 0) {
            return {
                eventSource,
                totalEngagements: 0,
                totalTraffic: 0,
                engagementRate: 0,
                timeSaved: "0",
                totalVideoViews: 0,
                averageWatchTime: "0:00",
                completedWatchRate: "0",
                totalQueries: 0,
                inferredEvents: 0,
            };
        }

        const aiQueryEvents = countType(events, "event_category", "search_query");
        const setVideoEvents = countType(events, "event_action", "setVideo");
        const playVideoEvents = countType(events, "event_action", "playVideo");
        const pauseVideoEvents = countType(events, "event_action", "pauseVideo");

        // Calculate inferred events only for embed.repd.us
        const inferredEventsCount = eventSource === 'embed.repd.us' ? this.calculateInferredEvents(events) : 0;

        const totalEngagements = events.length + inferredEventsCount;
        const totalTraffic = events.filter((item: any) => item.event_category === 'PageView' || item.event_action === 'module_open').length + inferredEventsCount;
        const engagementRate = totalTraffic > 0 ? ((totalEngagements / totalTraffic) * 100) : 0;
        const timeSaved = moment.duration({ "minutes": this.trendingQuestionAnalytics?.filter((query: interfaces.TrendingCategoryAnalyticsInterface) => query.source === "ai_queries").reduce((sum, q) => sum + Number(q.question_count), 0); * 5 }).asHours().toFixed(0);
        const totalVideoViews = setVideoEvents + playVideoEvents + pauseVideoEvents;

        // Calculate video stats using the reusable method
        const videoStats = this.calculateVideoStats(events);

        return {
            eventSource,
            totalEngagements,
            totalTraffic,
            engagementRate: parseFloat(engagementRate.toFixed(2)),
            timeSaved,
            totalVideoViews,
            averageWatchTime: videoStats.averageWatchTime,
            completedWatchRate: videoStats.completedWatchRate,
            totalQueries: aiQueryEvents,
            inferredEvents: inferredEventsCount,
        };
    }



    getEmbedWebappStatsCsv() {
        // Separate events by host
        const embedEvents = this.analytics.filter((event: any) => {
            console.log(event.host)
            return event.host && event.host.includes('embed.repd.us')
        }

        );
        const webappEvents = this.analytics.filter((event: any) =>
            !event.host || !event.host.includes('embed.repd.us')
        );

        // Calculate stats for embed events
        const embedStats = this.computeStatsForHostType(embedEvents, 'embed.repd.us');
        const webappStats = this.computeStatsForHostType(webappEvents, 'webapp');

        // Create proper CSV with single header row
        const headers = Object.keys(embedStats);
        const embedRow = headers.map(key => (embedStats as any)[key]);
        const webappRow = headers.map(key => (webappStats as any)[key]);

        // Build CSV manually to ensure proper format
        const csvContent = [
            headers.join(','), // Header row
            embedRow.join(','), // Embed data row
            webappRow.join(',')  // Webapp data row
        ].join('\n');

        return csvContent;
    }

    /**
     * Console helper function to export embed/webapp stats CSV
     * Usage from browser console: window.exportEmbedWebappStats()
     */
    exportEmbedWebappStatsToFile() {
        try {
            const csvContent = this.getEmbedWebappStatsCsv();
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', `embed-webapp-stats-${moment().format('YYYY-MM-DD-HHmm')}.csv`);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                console.log('✅ CSV exported successfully!');
            } else {
                console.error('❌ Browser does not support file download');
            }
        } catch (error) {
            console.error('❌ Error exporting CSV:', error);
        }
    }

    getEngagementsXlsx() {
        const data = [
            {
                sheet: "High Level Stats",
                columns: [
                    { label: "Total Traffic", value: "totalTraffic" },
                    { label: "Engagement Rate", value: "engagementRate" },
                    { label: "Time Saved", value: "timeSaved" },
                    { label: "Total Queries", value: "totalQueries" },
                ],
                content: [
                    {
                        totalTraffic: this.computedValues.totalTraffic,
                        engagementRate: this.computedValues.engagementRate,
                        timeSaved: this.computedValues.timeSaved,
                        totalQueries: this.computedValues.totalQueries,
                    }
                ]
            },
            {
                sheet: "Categories",
                columns: [
                    { label: "Category", value: "category" },
                    { label: "Sentiment", value: "sentiment" },
                    { label: "Count", value: "count" },
                    { label: "Volume Percentage", value: "volumePercentage" },
                    { label: "Volume Diff Percentage", value: "volumeDiffPercentage" },
                ],
                content: this.computedValues.topCategories?.map((category: any) => {
                    return {
                        category: category.category,
                        sentiment: category.sentiment,
                        count: category.count,
                        volumePercentage: category.volumePercentage,
                        volumeDiffPercentage: category.volumeDiffPercentage,
                    }
                })
            },
            {
                sheet: "Trending Data",
                columns: [
                    { label: "Category", value: "category" },
                    { label: "Sentiment", value: "sentiment" },
                    { label: "Question Count", value: "question_count" },
                ],
                content: [
                    ...this.computedValues.trendingData.queries,
                    ...this.computedValues.trendingData.questions
                ]
            }
        ];

        return data;
    }

    getVideosXlsx() {
        const data = [
            {
                sheet: "High Level Stats",
                columns: [
                    { label: "Total Video Views", value: "totalVideoViews" },
                    { label: "Average Watch Time", value: "averageWatchTime" },
                    { label: "Completed Watch Rate", value: "completedWatchRate" },
                ],
                content: [
                    {
                        totalVideoViews: this.computedValues.totalVideoViews,
                        averageWatchTime: this.computedValues.averageWatchTime,
                        completedWatchRate: this.computedValues.completedWatchRate,
                    }
                ]
            },
        ];

        return data;
    }

    getEmailsXlsx() {
        const data = [
            {
                sheet: "High Level Stats",
                columns: [
                    { label: "Delivered Emails", value: "deliveredEmails" },
                    { label: "Opened Emails", value: "openedEmails" },
                    { label: "Clicked Emails", value: "clickedEmails" },
                    { label: "Opened Emails Percentage", value: "openedEmailsPercentage" },
                    { label: "Clicked Emails Percentage", value: "clickedEmailsPercentage" },
                ],
                content: [
                    {
                        deliveredEmails: this.computedValues.deliveredEmails,
                        openedEmails: this.computedValues.openedEmails,
                        clickedEmails: this.computedValues.clickedEmails,
                        openedEmailsPercentage: this.computedValues.openedEmailsPercentage,
                        clickedEmailsPercentage: this.computedValues.clickedEmailsPercentage,
                    }
                ]
            },
            {
                sheet: "Bulk Send Data",
                columns: [
                    { label: "Date", value: "date" },
                    { label: "Subject", value: "subject" },
                    // Commented out columns that are no longer computed:
                    // { label: "List Name", value: "saved_list_name" },
                    // { label: "Delivered", value: "delivered" },
                    // { label: "Opens", value: "opensString" },
                    // { label: "Clicks", value: "clicksString" },
                    // { label: "Unsubscribes", value: "unsubscribesString" },
                    // { label: "Opens Percentage", value: "avgOpensPercentage" },
                    // { label: "Blocks Percentage", value: "blocksPercentage" },
                ],
                content: [...this.computedValues.bulkSendData]
            }
        ];

        return data;
    }

    ngpVanStats(question: string, listId: string): interfaces.NgpVanStatDisplayInterface {
        var result: interfaces.NgpVanStatDisplayInterface = {
            opensTotal: 0,
            opensUnique: 0,
            ctaTotal: 0,
            clicksUnique: 0,
            unsubscribes: 0,
            donations: 0
        };

        if (this.stats && this.stats.emails)
            for (var i = 0; i < this.stats.emails.length; i++) {
                const emailStats = ((this.stats.emails[i] || {}).emails || []);

                if (this.stats.emails[i].emails.length === 0 || (question !== 'all' && (this.stats.emails[i].question !== question))) continue;

                for (var i2 = 0; i2 < emailStats.length; i2++) {
                    const emailStat = emailStats[i2];

                    if (!emailStat || (listId !== 'all' && (emailStat.savedList !== listId))) continue;

                    result.opensTotal += emailStat.opensTotal
                    result.opensUnique += emailStat.opensUnique

                    result.ctaTotal += emailStat.ctaTotal
                    result.clicksUnique += emailStat.clicksUnique

                    result.unsubscribes += emailStat.unsubscribes
                    result.donations += emailStat.donations
                }
            }

        return result;
    }

    getNgpVanDate(question: string) {
        var result: string = '';

        if (this.stats && this.stats.emails) {
            for (var i = 0; i < this.stats.emails.length; i++)
                if (question === this.stats.emails[i].question)
                    result = this.stats.emails[i].date
        } else
            return 'Date Sent: -';

        if (!result && this.stats?.emails?.length > 0)
            result = this.stats?.emails[0]?.date || ''

        return `Sent On - ${result}`;
    }

    getNgpVanTimesSent(question: string) {
        var result: string = '';

        if (this.stats && this.stats.emails) {
            for (var i = 0; i < this.stats.emails.length; i++)
                if (question === this.stats.emails[i].question)
                    result = this.stats.emails[i].sends || ''
        } else
            return 'Number of Times Sent: -';

        if (!result && this.stats?.emails?.length > 0)
            result = this.stats?.emails[0]?.sends || '1'

        return `Sent - ${result} times`;
    }
}
