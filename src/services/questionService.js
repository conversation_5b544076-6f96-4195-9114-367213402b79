import { Op } from 'sequelize';

import questionError from '../errors/questionError.js';
import questionModel from '../models/questionModel.js';
import serviceUtil from '../utils/serviceUtil.js';
import questionMapper from '../mappers/questionMapper.js';

import answerModel from '../models/answerModel.js';
import userModel from '../models/userModel.js';
import voteModel from '../models/voteModel.js';
import questionMailer from '../mailers/questionMailer.js';
import clientModel from '../models/clientModel.js';
import questionShareModel from '../models/questionShareModel.js';

import getCategoryIcon from '../helpers/categoryHelper.js';
import obsceneWords from '../helpers/obsceneWordsList.js';
import harmfulWords from '../helpers/harmfulWordsList.js';
import { translateQuestion } from '../global/translation.js';

import { superSearch } from './_globals.js';

// Services
function create( criteria, auth ) {
	criteria.enabled = true;

	if ( criteria.generated ) criteria.userId = 1;

	const entityToCreate = {};
	serviceUtil.updateProperties( entityToCreate, transformToEntity( criteria, auth ) );

	const getQuestions = () => {
		return questionModel.findAndCountAll( {
			where: { enabled: true, clientId: entityToCreate.clientId, isAnswered: false }
		} );
	};

	const createQuestion = (questions) => {
		const questionText = entityToCreate.text.toLowerCase().replace(/\s+/g, ' ');
		const questionWords = questionText.match(/[\w'-]{2,}/g);

		const isObscene = (obsceneWords.concat(harmfulWords)).find(word => {
			if (word.includes(' ')) {

				return questionText.includes(word);
			}

			return questionWords.includes(word);
		});

		if (isObscene) {
			console.log('Inappropriate language detected in question: ', questionText);
			return Promise.reject( questionError.inappropriateLanguage() );
		}

		// Check for duplicates only within the same client
		const isDuplicate = questions.rows.map( (x) => x.text && x.clientId === entityToCreate.clientId)
			.join('@@#===').toLowerCase().replace( /[^0-9a-z@#=]+/ig )
			.split('@@#===').includes(
				entityToCreate.text.toLowerCase().replace( /[^0-9a-z]+/ig )
			);

		if (isDuplicate)
			return Promise.reject( questionError.questionDuplicated( criteria.text ) );

		return questionModel.create( entityToCreate );
	};

	const sendEmails = (question) => {
		if (question.id)
			questionModel.findOne(
				{ where: { id: question.id },
					id: question.id,
					include: [
						{ as: 'client', model: clientModel, required: false },
						{ as: 'user', model: userModel, required: false }
					]
				}
			).then( async ( question ) => {
				questionMailer.sendNewQuestionEmail( question );

				if ( question.user.email )
					questionMailer.sendApprovalPendingQuestionEmail( question );
			} );

		return question;
	};

	return getQuestions().then( createQuestion )
		.then( translateQuestion ).then( sendEmails )
		.then( questionMapper.toFullDTO );
}

function update ( criteria, auth ) {
	const getQuestion = () => {
		return questionModel.findOne( {
			where: { id: criteria.id },
			include: [
				{ as: 'user', model: userModel, required: false },
				{ as: 'client', model: clientModel, required: false } ]
		} );
	};

	return getQuestion().then( question => {
		var updateCriteria = {};

		if ( auth.user && [ 'admin', 'manager' ].includes( auth.user.accessLevel ) )
			updateCriteria = {
				text: criteria.text,
				suggestedNotes: criteria.suggestedNotes,
				transcript: criteria.transcript,
				isApproved: criteria.isApproved,
				isDenied: criteria.isDenied,
				enabled: criteria.isApproved === null ? criteria.enabled : criteria.isApproved
			};

		if ( updateCriteria.isApproved )
			questionMailer.sendApprovedQuestionEmail( question );

		return question.update( updateCriteria );
	} ).then( getQuestion
	).then( questionMapper.toFullDTO );
}

function search( criteria, auth, clientId, skipLimit ) {
	const getClientBlockedUsers = async() => {
		const client = await clientModel.findByPk(clientId);
		return client.blockedUsersIds;
	};

	const getQuestions = async(blockedUsersIds) => {
		criteria.include = [
			{ as: 'user', model: userModel, where: { enabled: true } },
			{ as: 'votes', model: voteModel, where: { enabled: true }, required: false },
			{ as: 'answers', model: answerModel, where: { enabled: true }, required: false },
			{ 
				as: 'question_shares', 
				model: questionShareModel, 
				where: { enabled: true }, 
				required: false,
				include: [
					{ 
						as: 'user', 
						model: userModel, 
						where: { enabled: true },
						required: false
					}
				] 
			}
		];

		const [count, questions] = await superSearch( [
			{ name: 'category', type: 'string' },
			{ name: 'text', type: 'string' }
		], questionModel, questionMapper, criteria, auth, skipLimit );

		return [count, questions.map((q) => {
			q.blockedForClient = blockedUsersIds.includes(Number(q.user.id));
			return q;
		})];
	};

	return getClientBlockedUsers().then(getQuestions);
}

function disable( id, auth ) {
	return(
		questionModel.findByIdAndEnabled( id )
			.then( question => {
				question.enabled = false;
				return question.save();
			} )
			.then( questionMapper.toFullDTO )
	);
}

// Private
function transformToEntity( criteria, auth ) {
	var result = [
		{ name: 'clientId' 				, value: criteria.clientId },
		{ name: 'userId'   				, value: criteria.userId   },
		{ name: 'text'     				, value: criteria.text     },
		{ name: 'originalLanguage'		, value: criteria.originalLanguage },
		{ name: 'category' 				, value: criteria.category },
		{ name: 'overridingName'		, value: criteria.overridingName },
		{ name: 'enabled'  				, value: true              } ];

	result[result.length] = {
		name: 'categoryIcon', value: getCategoryIcon( criteria )
	};

	if ( auth.user && [ 'admin', 'manager' ].includes( auth.user.accessLevel ) ) {
		result[result.length] = { name: 'isApproved' , value: criteria.isApproved };

		if (criteria.isDenied === true || criteria.isDenied === false)
			result[result.length] = { name: 'isDenied' , value: criteria.isDenied };
	}

	return result;
}

export default {
	create:  create,
	update:  update,
	search:  search,
	disable: disable
};
