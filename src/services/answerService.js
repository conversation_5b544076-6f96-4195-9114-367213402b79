// import { Op } from 'sequelize';

import answerModel from '../models/answerModel.js';
import serviceUtil from '../utils/serviceUtil.js';
import answerMapper from '../mappers/answerMapper.js';

import questionModel from '../models/questionModel.js';
import questionShareModel from '../models/questionShareModel.js';
import userModel from '../models/userModel.js';
import voteModel from '../models/voteModel.js';
import likeModel from '../models/likeModel.js';
import clientModel from '../models/clientModel.js';

import answerMailer from '../mailers/answerMailer.js';
import { startTranscriptionJob } from '../global/transcription.js';
import { superSearch } from './_globals.js';

import invokeLambda from '../global/invokeLambda.js';
import lambdaNames from '../global/lambdaNames.js';

function getConversionPayload(key, duration) {
	return {
		body: { sourceKey: key, duration }
	};
}
async function convertToMp4(key, duration) {
	console.log('Converting file to MP4');

	return invokeLambda(lambdaNames.mp4Converter, getConversionPayload(key, duration));
}
async function convertToOgv(key, duration) {
	console.log('Converting file to OGV');

	return invokeLambda(lambdaNames.ogvConverter, getConversionPayload(key, duration));
}
async function convertToWebm(key, duration) {
	console.log('Converting file to WEBM');

	return invokeLambda(lambdaNames.webmConverter, getConversionPayload(key, duration));
}

// Services
async function create(criteria, auth) {
	const entityToCreate = {};
	const questionShare = await questionShareModel.findOne({
		where: {
			questionId: criteria.questionId,
			userId: auth.user.id,
			// isAnswered: false,
		},
		include: [
			{
				as: 'user',
				model: userModel,
				required: false,
			},
		],
	});

	criteria.userId = auth.user.id;

	const isNotAdmin = auth.user.accessLevel !== 'admin' && auth.user.accessLevel !== 'manager';
	const isManager = auth.user.accessLevel === 'manager';

	if (isNotAdmin && criteria.questionId) {
		const question = await questionModel.findOne({
			where: { id: criteria.questionId },
		});

		if (question.isShared === true) {
			criteria.isShared = true;
			criteria.isDraft = true;

			// Validate an answer for the given question is correct
			if (!questionShare || questionShare.questionId !== question.id)
				throw new Error('You can not answer this question');
		}
	}
	if (isManager) {
		criteria.isDraft = true;
	}

	serviceUtil.updateProperties(
		entityToCreate,
		transformToEntity(criteria, auth)
	);

	return answerModel
		.create(entityToCreate)
		.then(async (answer) => {
			try {
				const result = await getAnswer(answer.id);
				let shouldDisableQuestion = true;

				if (criteria.isShared && questionShare) {
					questionShare.isAnswered = true;
					questionShare.enabled = false;
					questionShare.answerId = result.id;
					await questionShare.save();

					const questionShares = await questionShareModel.findAll({
						where: {
							questionId: criteria.questionId,
							clientId: criteria.clientId,
						},
					});
					const answeredQuestionShares = questionShares.filter((share) => {
						return share.isAnswered;
					});

					if (questionShares.length > answeredQuestionShares.length) {
						shouldDisableQuestion = false;
					}

					invokeLambda(lambdaNames.emailSender, {
						templateName: 'sharedVideoReceived',
						payload: { answer: result, questionShare },
					}).catch(console.error);
				}
				// Moved to create service:
				// else if (!criteria.isDraft) {
				// 	invokeLambda(lambdaNames.emailSender, {
				// 		templateName: 'videoReceived',
				// 		payload: result,
				// 	}).catch(console.error);
				// }

				const key = criteria.videoUrl.split('raw/')[1] || criteria.videoUrl.split('video_parts/')[1];
				const duration = criteria.videoDuration;
				console.log(`CRITERIA: ${JSON.stringify(criteria)}`);
				console.log(`KEY: ${key}`);
				console.log(`DURATION: ${duration}`);

				try {
					// First trigger the conversions
					await Promise.all([
						convertToMp4(key, duration),
						convertToOgv(key, duration),
						convertToWebm(key, duration)
					]);
					
					// Add a delay to allow conversions to complete before transcription starts
					console.log('Waiting for conversions to complete before starting transcription...');
					await new Promise(resolve => setTimeout(resolve, 10000));

					if (shouldDisableQuestion) {
						await setQuestionToAnswered(result.questionId, result.question);
					}
					
					return answer;
				} catch (error) {
					console.error('Video conversion failed:', error);
					throw new Error('Video conversion failed: ' + error.message);
				}

				return answer;
			} catch (error) {
				console.error('Answer creation failed:', error);
				throw error;
			}
		})
		// .then(translateSubtitles)
		.then(startTranscriptionJob)
		.then(answerMapper.toFullDTO);
}

function update(criteria, auth) {
	return getAnswer(criteria.id).then(async (answer) => {
		if (!answer) throw new Error('Please update an answer that exists');
		else {
			if (
				(!criteria.isDraft || answer.isShared) &&
				criteria.isApproved &&
				answer.enabled !== false
			) {
				answerMailer.sendApprovedAnswerEmail(answer);
				setQuestionToAnswered(answer.questionId);
			} else if (
				(criteria.isDraft || answer.isShared) &&
				criteria.isDenied &&
				answer.enabled !== false
			)
				answerMailer.sendDeniedAnswerEmail(answer);

			const entityToUpdate = answer.dataValues;

			// console.log('entityToUpdate', entityToUpdate);

			// Transform criteria and auth to entity
			const transformedEntity = transformToEntity(criteria, auth);

			// Update properties of entityToUpdate
			serviceUtil.updateProperties(entityToUpdate, transformedEntity);

			for (const key of Object.keys(criteria)) {
				// Ignore keys that are not in the answer model
				if (!answerModel.rawAttributes[key]) continue;
				answer.changed(key, true);
			}

			return answer.update(entityToUpdate).then(startTranscriptionJob).then(answerMapper.toDTO);
		}
	});
}

function publish(criteria, auth) {
	return getAnswer(criteria.id).then(async (answer) => {
		if (!answer) throw new Error('Please publish an answer that exists');
		else {
			const entityToCreate = {};

			serviceUtil.updateProperties(
				entityToCreate,
				transformToEntity(criteria, auth)
			);

			const updateResult = await answer.update(entityToCreate).then(answerMapper.toDTO);

			if (updateResult.isDraft) 
				answerMailer.sendAnsweredQuestionShareEmail(answer);

			// Send email notification when answer is published
			// await invokeLambda(lambdaNames.emailSender, {
			// 	templateName: 'answeredQuestion',
			// 	payload: updateResult,
			// }).catch(console.error);

			// Update status
			await invokeLambda(lambdaNames.statusUpdater, {
				body: {
					publish: {
						answerId: criteria.id
					}
				}
			});

			return updateResult;
		}
	});
}

function replace(criteria, auth) {
	return getAnswer(criteria.id).then(async (answer) => {
		if (!answer) throw new Error('Please replace an answer that exists');
		else {
			const entityToCreate = {};

			serviceUtil.updateProperties(
				entityToCreate,
				transformToEntity(criteria, auth)
			);

			return answer.update(entityToCreate).then(answerMapper.toDTO);
		}
	});
}

function search(criteria, auth, skipLimit) {
	criteria.include = [
		{
			as: 'question',
			model: questionModel,
			include: [
				{ as: 'user', model: userModel, where: { enabled: true } },
				{
					as: 'votes',
					model: voteModel,
					where: { enabled: true },
					required: false,
				},
			],
		},
		{
			as: 'likes',
			model: likeModel,
			where: { enabled: true },
			required: false,
		},
		// { as: 'comments', model: commentModel, where: { enabled: true }, required: false,
		// 	include: [ { as: 'user', model: userModel, where: { enabled: true } } ]
		// },
		{ as: 'question_shares', model: questionShareModel, required: false },
		{ as: 'user', model: userModel, required: false },
	];

	return superSearch([], answerModel, answerMapper, criteria, auth, skipLimit);
}

function disable(id, auth) {
	return answerModel
		.findByIdAndEnabled(id)
		.then((answer) => {
			answer.enabled = false;
			return answer.save();
		})
		.then((answer) => {
			answerModel
				.findOne({
					where: { id: answer.id },
					id: answer.id,
					include: [{ as: 'question', model: questionModel }],
				})
				.then((result) => {
					result.question.isAnswered = false;
					result.question.enabled = true;
					result.question.save();

					return result;
				});

			return answer;
		})
		.then((answer) => {
			questionShareModel
				.findAll({ where: { questionId: answer.questionId } })
				.then((questionShares) => {
					questionShares.forEach((questionShare) => {
						questionShare.enabled = false;
						questionShare.isAnswered = true;
						questionShare.save();
					});
				});
			return answer;
		})
		.then(answerMapper.toFullDTO);
}

// Private

function getAnswer(answerId) {
	return answerModel.findOne({
		where: { id: answerId, enabled: true },
		id: answerId,
		include: [
			{ as: 'user', model: userModel, required: false },
			{ as: 'client', model: clientModel, required: false },
			{
				as: 'question',
				model: questionModel,
				isAnswered: false,
				enabled: true,
				required: false,
				include: [
					{ as: 'user', model: userModel, required: false },
					{
						where: { enabled: true },
						as: 'votes',
						model: voteModel,
						include: [{ as: 'user', model: userModel, required: false }],
						required: false,
					},
				],
			},
		],
	});
}

async function setQuestionToAnswered(id, question = null) {
	if (!question) question = await questionModel.findOne({ where: { id } });

	question.isAnswered = true;
	question.enabled = false;
	question.save();
}

function transformToEntity(criteria, auth) {
	if (criteria.isApproved) {
		criteria.isDenied = false;
		criteria.isDraft = false;
	}

	const fields = [
		{ name: 'userId', value: criteria.userId },
		{ name: 'clientId', value: criteria.clientId },
		{ name: 'questionId', value: criteria.questionId },
		{ name: 'imageUrl', value: criteria.imageUrl },
		{ name: 'videoUrl', value: criteria.videoUrl },
		{ name: 'videoUrls', value: criteria.videoUrls },
		{ name: 'subtitles', value: criteria.subtitles },
		{ name: 'subtitlesSpeed', value: criteria.subtitlesSpeed },
		{ name: 'transcription', value: criteria.transcription },
		{ name: 'showTranscribedSubtitles', value: !!criteria.showTranscribedSubtitles },
		{ name: 'isDraft', value: criteria.isDraft },
		{ name: 'isApproved', value: criteria.isApproved },
		{ name: 'isDenied', value: criteria.isDenied },
		{ name: 'isPinned', value: criteria.isPinned },
		{ name: 'uploadId', value: criteria.uploadId },
		{ name: 'videoKey', value: criteria.videoKey },
		{ name: 'isShared', value: criteria.isShared },
		{ name: 'enabled', value: true },
		{ name: 'endDate', value: criteria.endDate },
		// Set video status fields to "unprocessed" when creating a new answer
		{ name: 'mp4VideoStatus', value: 'unprocessed' },
		{ name: 'ogvVideoStatus', value: 'unprocessed' },
		{ name: 'webmVideoStatus', value: 'unprocessed' },
	];

	if (criteria.endDate === '') fields[fields.length - 4].value = null;

	if (criteria.videoDuration)
		fields[fields.length] = {
			name: 'videoDuration',
			value: criteria.videoDuration,
		};

	return fields;
}

export default {
	getAnswer,
	create,
	update,
	publish,
	replace,
	search,
	disable,
};
