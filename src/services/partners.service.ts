import { ApiService } from './api.service';

export interface CreatePartnerRequest {
  name: string;
  features: string[];
  clientIds: string[];
  apiEnabled?: boolean;
}

export interface ApiKey {
  id: string;
  key: string;
  keySample: string;
  clientId: string;
  createdAt: string;
  updatedAt: string;
}

export interface RawApiKey {
  clientId: string;
  rawKey: string;
  apiKeyId: string;
}

export interface RegenerateApiKeyResponse {
  message: string;
  oldApiKeyId: string;
  newApiKey: {
    id: string;
    clientId: number;
    rawKey: string;
    keySample: string;
  };
}

export interface PartnerResponse {
  id: string;
  name: string;
  features: string[];
  clientIds: string[];
  enabled: boolean;
  apiEnabled: boolean;
  apiKeyIds: string[];
  apiKeys: ApiKey[];
  rawApiKeys: RawApiKey[];
  createdAt: string;
  updatedAt: string;
}

export class PartnersService extends ApiService {
  id: string;
  partners: PartnerResponse[] = [];

  constructor(token?: string, clientId?: string) {
    super(token, clientId);
    this.id = 'Partners';
    console.log('PartnersService constructor - token:', token ? 'Present' : 'Missing');
  }

  async getAllPartners(callback?: (partners: PartnerResponse[]) => void): Promise<PartnerResponse[]> {
    const endpoint = `${this.base}${this.paths.partners}`;
    const errorIdentifier = `${this.id} -> Get All Partners`;

    console.log('Making request to:', endpoint);
    console.log('Request headers:', this.axiosInstance.defaults.headers);

    try {
      const response = await this.axiosInstance.get(endpoint);
      const responseData: PartnerResponse[] = response.data.data;

      if (!responseData || response.data.errors) {
        this.reportInvalidResponseError(errorIdentifier, response);
        throw new Error('Invalid response from server');
      }

      this.partners = responseData;

      if (callback) callback(responseData);
      return responseData;
    } catch (error: any) {
      this.reportRequestError(errorIdentifier, error);
      throw error;
    }
  }

  async createPartner(partnerData: CreatePartnerRequest, callback?: (partner: PartnerResponse) => void): Promise<PartnerResponse> {
    const endpoint = `${this.base}${this.paths.partners}`;
    const errorIdentifier = `${this.id} -> Create Partner`;

    try {
      const response = await this.axiosInstance.post(endpoint, partnerData);
      const responseData: PartnerResponse = response.data.data[0];

      if (!responseData || response.data.errors) {
        this.reportInvalidResponseError(errorIdentifier, response);
        throw new Error('Invalid response from server');
      }

      if (callback) callback(responseData);
      return responseData;
    } catch (error: any) {
      this.reportRequestError(errorIdentifier, error);
      throw error;
    }
  }

  async updatePartner(partnerId: string, partnerData: Partial<CreatePartnerRequest>, callback?: (partner: PartnerResponse) => void): Promise<PartnerResponse> {
    const endpoint = `${this.base}${this.paths.partners}/${partnerId}`;
    const errorIdentifier = `${this.id} -> Update Partner`;

    try {
      const response = await this.axiosInstance.put(endpoint, partnerData);
      const responseData: PartnerResponse = response.data.data[0];

      if (!responseData || response.data.errors) {
        this.reportInvalidResponseError(errorIdentifier, response);
        throw new Error('Invalid response from server');
      }

      if (callback) callback(responseData);
      return responseData;
    } catch (error: any) {
      this.reportRequestError(errorIdentifier, error);
      throw error;
    }
  }

  async revokeApiKey(partnerId: string, clientId: string, callback?: (response: any) => void): Promise<any> {
    const endpoint = `${this.base}${this.paths.partners}/${partnerId}/clients/${clientId}/api-key/revoke`;
    const errorIdentifier = `${this.id} -> Revoke API Key`;

    try {
      const response = await this.axiosInstance.post(endpoint);
      const responseData = response.data.data[0];

      if (!responseData || response.data.errors) {
        this.reportInvalidResponseError(errorIdentifier, response);
        throw new Error('Invalid response from server');
      }

      if (callback) callback(responseData);
      return responseData;
    } catch (error: any) {
      this.reportRequestError(errorIdentifier, error);
      throw error;
    }
  }

  async regenerateApiKey(partnerId: string, clientId: string, callback?: (response: RegenerateApiKeyResponse) => void): Promise<RegenerateApiKeyResponse> {
    const endpoint = `${this.base}${this.paths.partners}/${partnerId}/clients/${clientId}/api-key/regenerate`;
    const errorIdentifier = `${this.id} -> Regenerate API Key`;

    try {
      const response = await this.axiosInstance.post(endpoint);
      const responseData: RegenerateApiKeyResponse = response.data.data[0];

      if (!responseData || response.data.errors) {
        this.reportInvalidResponseError(errorIdentifier, response);
        throw new Error('Invalid response from server');
      }

      if (callback) callback(responseData);
      return responseData;
    } catch (error: any) {
      this.reportRequestError(errorIdentifier, error);
      throw error;
    }
  }
}
