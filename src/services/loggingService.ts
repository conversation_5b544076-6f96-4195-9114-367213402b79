import { ApiService } from './api.service';

interface LogEntry {
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  timestamp?: string;
  source?: string;
  url?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
  action?: string;
  category?: string;
  retryCount?: number;
}

interface LogOptions {
  metadata?: Record<string, any>;
  action?: string;
  category?: string;
}

/**
 * Service for sending logs from the admin panel to the server
 */
class LoggingService {
  private apiService: ApiService;
  private logQueue: LogEntry[];
  private isProcessing: boolean;
  private batchSize: number;
  private flushInterval: number;

  constructor() {
    this.apiService = new ApiService();
    this.logQueue = [];
    this.isProcessing = false;
    this.batchSize = 10;
    this.flushInterval = 5000; // 5 seconds
    
    // Start the batch processing
    this.startBatchProcessing();
  }

  /**
   * Log a message to the server
   */
  async log(level: LogEntry['level'], message: string, options: LogOptions = {}): Promise<void> {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      source: 'admin-panel',
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...options
    };

    // Add to queue for batch processing
    this.logQueue.push(logEntry);

    // If it's an error, send immediately
    if (level === 'error') {
      await this.flushLogs();
    }
  }

  /**
   * Log an error message
   */
  async error(message: string, metadata: Record<string, any> = {}): Promise<void> {
    return this.log('error', message, { metadata });
  }

  /**
   * Log a warning message
   */
  async warn(message: string, metadata: Record<string, any> = {}): Promise<void> {
    return this.log('warn', message, { metadata });
  }

  /**
   * Log an info message
   */
  async info(message: string, metadata: Record<string, any> = {}): Promise<void> {
    return this.log('info', message, { metadata });
  }

  /**
   * Log a debug message
   */
  async debug(message: string, metadata: Record<string, any> = {}): Promise<void> {
    return this.log('debug', message, { metadata });
  }

  /**
   * Log user actions for analytics
   */
  async logUserAction(action: string, category: string, metadata: Record<string, any> = {}): Promise<void> {
    return this.log('info', `User action: ${action}`, {
      action,
      category,
      metadata
    });
  }

  /**
   * Log API errors
   */
  async logApiError(endpoint: string, error: any, requestData: Record<string, any> = {}): Promise<void> {
    const errorMetadata = {
      endpoint,
      errorMessage: error.message,
      errorCode: error.code,
      status: error.response?.status,
      statusText: error.response?.statusText,
      requestData: JSON.stringify(requestData).substring(0, 1000), // Limit size
      responseData: error.response?.data ? JSON.stringify(error.response.data).substring(0, 1000) : null
    };

    return this.log('error', `API Error: ${endpoint}`, {
      metadata: errorMetadata
    });
  }

  /**
   * Log performance metrics
   */
  async logPerformance(operation: string, duration: number, metadata: Record<string, any> = {}): Promise<void> {
    return this.log('info', `Performance: ${operation} took ${duration}ms`, {
      action: 'performance',
      category: 'metrics',
      metadata: {
        operation,
        duration,
        ...metadata
      }
    });
  }

  /**
   * Start batch processing of logs
   */
  private startBatchProcessing(): void {
    setInterval(() => {
      if (this.logQueue.length > 0) {
        this.flushLogs();
      }
    }, this.flushInterval);
  }

  /**
   * Flush all queued logs to the server
   */
  async flushLogs(): Promise<void> {
    if (this.isProcessing || this.logQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Take logs from queue
      const logsToSend = this.logQueue.splice(0, this.batchSize);

      if (logsToSend.length === 1) {
        // Send single log
        await this.apiService.post('logs', logsToSend[0]);
      } else if (logsToSend.length > 1) {
        // Send batch
        await this.apiService.post('logsBatch', { logs: logsToSend });
      }

    } catch (error: any) {
      // If logging fails, we don't want to create an infinite loop
      // Just log to console as fallback
      console.error('Failed to send logs to server:', error);
      
      // Put logs back in queue for retry (but limit retries)
      const logsWithRetry = logsToSend.map(log => ({
        ...log,
        retryCount: (log.retryCount || 0) + 1
      })).filter(log => (log.retryCount || 0) < 3); // Max 3 retries

      this.logQueue.unshift(...logsWithRetry);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Force flush all logs immediately
   */
  async forceFlush(): Promise<void> {
    while (this.logQueue.length > 0) {
      await this.flushLogs();
      // Small delay to prevent overwhelming the server
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
}

// Create singleton instance
const loggingService = new LoggingService();

// Auto-flush logs when page is about to unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    // Use sendBeacon for reliable delivery during page unload
    if (loggingService.logQueue.length > 0 && navigator.sendBeacon) {
      const logs = loggingService.logQueue.splice(0);
      const payload = JSON.stringify({ logs });
      
      try {
        navigator.sendBeacon(
          `${process.env.REACT_APP_API_URL}/api/v1.0.0/logs/batch`,
          payload
        );
      } catch (error) {
        console.error('Failed to send logs via beacon:', error);
      }
    }
  });

  // Also capture unhandled errors
  window.addEventListener('error', (event) => {
    loggingService.error('Unhandled JavaScript error', {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error?.stack
    });
  });

  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    loggingService.error('Unhandled promise rejection', {
      reason: event.reason?.toString(),
      stack: event.reason?.stack
    });
  });
}

export default loggingService;
