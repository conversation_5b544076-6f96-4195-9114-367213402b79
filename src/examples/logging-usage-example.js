/**
 * Example usage of the logging service in the admin panel
 * This file demonstrates how to integrate logging into React components
 */

import React, { useEffect, useState } from 'react';
import loggingService from '../services/loggingService';

// Example 1: Basic component with logging
const QuestionForm = () => {
  const [question, setQuestion] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    // Log when component mounts
    loggingService.info('QuestionForm component mounted');
    
    // Log user action
    loggingService.logUserAction('page_view', 'question_management', {
      component: 'QuestionForm'
    });

    return () => {
      // Log when component unmounts
      loggingService.debug('QuestionForm component unmounted');
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    const startTime = performance.now();

    try {
      // Log the start of the operation
      loggingService.info('Starting question submission', {
        questionLength: question.length
      });

      // Simulate API call
      const response = await fetch('/api/v1.0.0/questions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ text: question })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      const duration = performance.now() - startTime;

      // Log successful submission
      loggingService.info('Question submitted successfully', {
        questionId: result.data[0]?.id,
        duration
      });

      // Log performance
      loggingService.logPerformance('question_submission', duration, {
        questionLength: question.length,
        success: true
      });

      // Log user action
      loggingService.logUserAction('submit_question', 'question_management', {
        questionId: result.data[0]?.id,
        questionLength: question.length
      });

      setQuestion('');
      alert('Question submitted successfully!');

    } catch (error) {
      const duration = performance.now() - startTime;

      // Log the error
      loggingService.error('Failed to submit question', {
        error: error.message,
        questionLength: question.length,
        duration
      });

      // Log API error specifically
      loggingService.logApiError('/api/v1.0.0/questions', error, {
        text: question
      });

      // Log performance for failed operation
      loggingService.logPerformance('question_submission', duration, {
        questionLength: question.length,
        success: false,
        error: error.message
      });

      alert('Failed to submit question. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (e) => {
    setQuestion(e.target.value);
    
    // Log user interaction (debounced to avoid spam)
    if (e.target.value.length % 50 === 0 && e.target.value.length > 0) {
      loggingService.debug('User typing in question field', {
        currentLength: e.target.value.length
      });
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label htmlFor="question">Question:</label>
        <textarea
          id="question"
          value={question}
          onChange={handleInputChange}
          placeholder="Enter your question here..."
          required
        />
      </div>
      <button 
        type="submit" 
        disabled={isSubmitting}
        onClick={() => {
          // Log button click
          loggingService.logUserAction('click_submit', 'question_management', {
            buttonState: isSubmitting ? 'disabled' : 'enabled',
            questionLength: question.length
          });
        }}
      >
        {isSubmitting ? 'Submitting...' : 'Submit Question'}
      </button>
    </form>
  );
};

// Example 2: Error boundary with logging
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to the server
    loggingService.error('React Error Boundary caught error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div>
          <h2>Something went wrong.</h2>
          <p>The error has been logged and will be investigated.</p>
        </div>
      );
    }

    return this.props.children;
  }
}

// Example 3: Custom hook for logging
const useLogging = (componentName) => {
  useEffect(() => {
    loggingService.info(`${componentName} mounted`);
    
    return () => {
      loggingService.debug(`${componentName} unmounted`);
    };
  }, [componentName]);

  const logUserAction = (action, metadata = {}) => {
    loggingService.logUserAction(action, 'user_interaction', {
      component: componentName,
      ...metadata
    });
  };

  const logError = (error, context = {}) => {
    loggingService.error(`Error in ${componentName}`, {
      error: error.message,
      stack: error.stack,
      component: componentName,
      ...context
    });
  };

  const logPerformance = (operation, startTime, metadata = {}) => {
    const duration = performance.now() - startTime;
    loggingService.logPerformance(`${componentName}_${operation}`, duration, {
      component: componentName,
      ...metadata
    });
  };

  return {
    logUserAction,
    logError,
    logPerformance
  };
};

// Example 4: Component using the custom hook
const UserList = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const { logUserAction, logError, logPerformance } = useLogging('UserList');

  useEffect(() => {
    const fetchUsers = async () => {
      const startTime = performance.now();
      
      try {
        const response = await fetch('/api/v1.0.0/users');
        const data = await response.json();
        
        setUsers(data.data || []);
        logPerformance('fetch_users', startTime, {
          userCount: data.data?.length || 0
        });
        
      } catch (error) {
        logError(error, { operation: 'fetch_users' });
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [logError, logPerformance]);

  const handleUserClick = (user) => {
    logUserAction('user_click', {
      userId: user.id,
      userEmail: user.email
    });
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h2>Users</h2>
      {users.map(user => (
        <div key={user.id} onClick={() => handleUserClick(user)}>
          {user.email}
        </div>
      ))}
    </div>
  );
};

// Example 5: Utility functions for common logging patterns
export const LoggingUtils = {
  // Log page navigation
  logPageView: (pageName, metadata = {}) => {
    loggingService.logUserAction('page_view', 'navigation', {
      page: pageName,
      url: window.location.href,
      ...metadata
    });
  },

  // Log form interactions
  logFormEvent: (formName, eventType, metadata = {}) => {
    loggingService.logUserAction(`form_${eventType}`, 'form_interaction', {
      form: formName,
      ...metadata
    });
  },

  // Log API calls
  logApiCall: async (endpoint, method, data = null) => {
    const startTime = performance.now();
    
    loggingService.debug(`API call started: ${method} ${endpoint}`, {
      endpoint,
      method,
      hasData: !!data
    });

    return {
      logSuccess: (response) => {
        const duration = performance.now() - startTime;
        loggingService.info(`API call successful: ${method} ${endpoint}`, {
          endpoint,
          method,
          duration,
          status: response.status
        });
      },
      logError: (error) => {
        const duration = performance.now() - startTime;
        loggingService.logApiError(endpoint, error, data);
        loggingService.logPerformance(`api_${method.toLowerCase()}`, duration, {
          endpoint,
          success: false
        });
      }
    };
  }
};

export default {
  QuestionForm,
  ErrorBoundary,
  useLogging,
  UserList,
  LoggingUtils
};
