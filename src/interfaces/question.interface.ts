import { UserInterface } from './user.interface';

import { supportedLanguages } from 'hooks/useTranslationContext.hook';

export interface QuestionInterface {
  id: string;
  clientId: string;

  text: string;
  originalLanguage: string;
  overridingName: string | null;
  translations: Record<typeof supportedLanguages[number], string>;
  category: string;
  // TODO: add type check for category icons it is limited list of strings
  categoryIcon: string;
  votes: number;

  // TODO: check if we receive here users?
  user: Partial<UserInterface>;
  userVotes: Partial<UserInterface>[] | any;

  isApproved: boolean | null;
  isDenied: boolean | null;
  isAnswered: boolean | null;
  asked: boolean;
  categorySummary: string;

  enabled: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
}

export interface NewQuestionData {
  userId?: string;
  clientId: string;
  votes: number;
  text: string;
  originalLanguage: string;
  name: string;
  category: string;
  categoryIcon: string;
  asked: boolean;
}

export interface VoteInterface {
  id: string;
  enabled: boolean;
  createdAt: string | Date;
  updatedAt: string | Date;
  userId: string;
  questionId: string;
  answerId: string | null;
  amount: number;
}
