import { useState, useCallback, useEffect, useRef } from "react";
import cn from "classnames";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSpinner } from "@fortawesome/free-solid-svg-icons";
import { toast } from 'react-toastify';

import * as interfaces from "interfaces";
import { useClientContext, useTranslationContext, useUserContext } from "hooks";
import { AnswerServiceInstance, TrackerServiceInstance } from "services";

import CounterButton from "shared/CounterButton";
import IconAvatar from "shared/IconAvatar";
import Icons from "shared/Icons";
import VideoControl from "./VideoControl";

import classes from "./SelectedAnswer.module.scss";
import ButtonClasses from "shared/Button/Button.module.scss";

import thumbsUp from 'assets/icons/thumbs-up.svg';
import thumbsDown from 'assets/icons/thumbs-down.svg';
import shareIcon from 'assets/icons/share.svg';
import arrowIcon from 'assets/icons/arrow-right.svg';
import cityIcon from 'assets/icons/city.svg';

const REPD_HOME_URL = 'https://www.repd.us';

const SHAREABLE_LINKS = [
  {
    icon: 'twitter',
    text: 'Share',
    key: 'twitter'
  },
  {
    icon: 'facebook-square',
    text: 'Share',
    key: 'facebook'
  },
  {
    icon: 'envelope',
    text: 'Share',
    key: 'email'
  },
  {
    icon: 'copy',
    text: 'Copy Link',
    key: 'copy'
  }
];

export interface VideoStateInterface {
  started: boolean;
  paused: boolean;
  seeking: boolean;
  seeked: boolean;
  ended: boolean;

  isFullScreen: boolean;
  isFeedbackShown: boolean;
  isFeedbackSubmitted: boolean;
  isDonationBannerShown: boolean;
  isMetaInfoShown: boolean;
}

export interface VideoSubtitleStateInterface {
  index: number;
  currentLine: string | null;
  list: any[] | string[];
  started: boolean;
  enabled: boolean;
}

export default function SelectedAnswer(props: {
  isMobile?: boolean;
  videoRef: any;
  selectedAnswer?: interfaces.AnswerInterface;
  handleVideoUpdate?: (a: interfaces.AnswerInterface) => void;
}) {
  const { isMobile, videoRef } = props;
  const {
    selectedAnswer,
    handleVideoUpdate = (a: interfaces.AnswerInterface) => { },
  } = props;
  const { client } = useClientContext();
  const { user } = useUserContext();
  const { t, translateCustomText, language } = useTranslationContext();

  const [videoState, setVideoState] = useState({} as VideoStateInterface);
  const [subtitles, setVideoSubtitles] = useState(
    {} as VideoSubtitleStateInterface
  );
  const [muted, setMuted] = useState<boolean>(false);
  const [showShareMenu, setShareMenu] = useState<boolean>(false);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const videoBackgroundContext = canvasRef.current?.getContext("2d");

  const drawBackground = () => {
    if (videoRef.current?.paused || videoRef.current?.ended) return;
    videoBackgroundContext?.drawImage(videoRef.current, 0, 0);
    setTimeout(drawBackground, 20);
  };

  const getQuestionTranslation = () => {
    const question = selectedAnswer?.question;

    // Debug logging to identify problematic data
    if (process.env.NODE_ENV === 'development') {
      console.log('DEBUG: selectedAnswer structure:', {
        id: selectedAnswer?.id,
        hasQuestion: !!selectedAnswer?.question,
        questionKeys: selectedAnswer?.question ? Object.keys(selectedAnswer.question) : [],
        userKeys: selectedAnswer?.question?.user ? Object.keys(selectedAnswer.question.user) : []
      });
    }

    if (question) {
      if (language !== question.originalLanguage && question.translations)
        return String(question.translations[language] || question.text || "");
      return String(question.text || "");
    }
    return "";
  };

  const videoIsPlaying: boolean =
    videoState.seeking ||
    (videoState.started && !videoState.paused && !videoState.ended);

  const videoIsPaused: boolean =
    (!videoState.started || videoState.paused) && !videoState.ended;

  const showMetaData: boolean =
    videoIsPaused && selectedAnswer?.question?.text !== undefined;

  const hideLikeButton = client.id === '275'; // Hide for Redondo PD
  const showAltMetaInfo = client.id === '283' || client.id === '15'; // Only for Emporia and Dave Cavell

  function setSubtitles() {
    let subtitlesText = "";

    if (
      language !== "en" &&
      selectedAnswer?.transcriptionTranslation &&
      selectedAnswer?.transcriptionTranslation[language]
    )
      subtitlesText = selectedAnswer?.transcriptionTranslation[language]?.map(
        (s) => s.transcript
      ).join(' ').replace(/ +/, ' ') || "";
    else subtitlesText =
      selectedAnswer?.transcription?.audio_segments?.map((s) => s.transcript).join(' ').replace(/ +/, ' ') ||
      selectedAnswer?.subtitles ||
      "";

    let lineWidth: number = 450, // pixels
      letterWidth: number = 8,
      letterCount: number = Math.floor(lineWidth / letterWidth),
      words: string[] = subtitlesText.split(" "),
      currentLineSize: number = 0,
      currentLine: string = "",
      lines: any[] = [];

    if (!subtitlesText || words.length === 1) return;

    words.forEach((word: string, i: number) => {
      const wordSize = word.length;

      if (currentLineSize + wordSize + 1 > letterCount) {
        lines[lines.length] = currentLine
          .replace(/^ /, "")
          .replace(/\s+/g, " ")
          .replace(/\\n/g, " ")
          .replace(/\\"/g, "")
          .replace(/\\/g, " ");
        currentLineSize = 0;
        currentLine = "";
      }

      currentLineSize += wordSize + 1;

      currentLine = `${currentLine} ${word}`;
    });

    setVideoSubtitles((c) => ({
      ...c,
      list: lines,
      enabled: !!selectedAnswer?.showTranscribedSubtitles && subtitlesText !== "",
    }));
  }

  function setSubtitle() {
    if (!subtitles.list) {
      console.log("DEBUG: No subtitles list available");
      return;
    }

    const videoDuration = videoRef?.current?.duration || 0;
    const videoTime = videoRef?.current?.currentTime || 0;
    const subtitleMax = subtitles.list.length;
    const indexMultiplier = subtitleMax / videoDuration;

    console.log(`DEBUG: Video time: ${videoTime.toFixed(2)}s, Duration: ${videoDuration.toFixed(2)}s`);
    console.log(`DEBUG: Subtitle count: ${subtitleMax}`);

    // Number of lines to display at once
    const linesToShow = 3; // Changed from 1 to 3 to show more context

    // Adjust videoTime if there are audio segments
    const audioSegments = selectedAnswer?.transcription?.audio_segments;
    if (audioSegments && audioSegments.length > 0) {
      console.log(`DEBUG: Using audio segments (${audioSegments.length} segments)`);

      // 1) total duration of all segments
      const totalSegmentDuration = audioSegments.reduce((acc, seg) => {
        return acc + (parseFloat(seg.end_time) - parseFloat(seg.start_time));
      }, 0);
      console.log(`DEBUG: Total audio duration: ${totalSegmentDuration.toFixed(2)}s`);

      // 2) find which segment we're in and how far in it
      let segmentIndex = -1;
      let elapsedInSegments = 0;

      // Check if we've started speaking yet - handle immediate speech differently
      const firstSegmentStart = parseFloat(audioSegments[0].start_time);
      const lastSegmentEnd = parseFloat(audioSegments[audioSegments.length - 1].end_time);

      console.log(`DEBUG: First segment starts at ${firstSegmentStart.toFixed(2)}s`);
      console.log(`DEBUG: Last segment ends at ${lastSegmentEnd.toFixed(2)}s`);
      console.log(`DEBUG: Speech starts immediately? ${firstSegmentStart <= 0.5 ? "Yes" : "No"}`);

      // Only treat as "before speech" if first segment starts after 0.5 seconds
      if (firstSegmentStart > 0.5 && videoTime < firstSegmentStart) {
        console.log("DEBUG: Before speech starts - showing empty subtitle");
        // Before first speech - show empty or initial subtitle
        setVideoSubtitles((c) => ({ ...c, index: 0, currentLine: "" }));
        return;
      }

      // Log all segments for debugging
      if (videoTime < 10) { // Only log during first 10 seconds to avoid spam
        audioSegments.forEach((seg, idx) => {
          const start = parseFloat(seg.start_time);
          const end = parseFloat(seg.end_time);
          console.log(`DEBUG: Segment ${idx}: ${start.toFixed(2)}s - ${end.toFixed(2)}s (${(end - start).toFixed(2)}s)`);
        });
      }

      for (let i = 0; i < audioSegments.length; i++) {
        const segStart = parseFloat(audioSegments[i].start_time);
        const segEnd = parseFloat(audioSegments[i].end_time);
        if (videoTime >= segStart && videoTime <= segEnd) {
          segmentIndex = i;
          // partial progress in current segment
          elapsedInSegments += (videoTime - segStart);
          console.log(`DEBUG: In segment ${i} (${segStart.toFixed(2)}s - ${segEnd.toFixed(2)}s), progress: ${(videoTime - segStart).toFixed(2)}s`);
          break;
        } else if (videoTime > segEnd) {
          // add entire segment length
          elapsedInSegments += (segEnd - segStart);
        } else {
          // haven't reached this segment yet
          break;
        }
      }

      // If speech starts immediately (within first 0.5s) and we're at the beginning
      if (firstSegmentStart <= 0.5 && videoTime < firstSegmentStart) {
        console.log("DEBUG: Speech starts immediately but we're before it - showing first subtitle");
        // Show the first subtitle with multiple lines
        const startIndex = 0;
        const endIndex = Math.min(subtitleMax - 1, linesToShow - 1);
        const combinedLines = subtitles.list.slice(startIndex, endIndex + 1).join("\n");

        setVideoSubtitles((c) => ({
          ...c,
          index: 0,
          currentLine: combinedLines
        }));
        return;
      }

      if (segmentIndex === -1) {
        console.log(`DEBUG: Not in any segment, videoTime: ${videoTime.toFixed(2)}s`);
        // After all segments or in a gap - show last subtitle or empty
        const lastIndex = subtitleMax - 1;

        if (videoTime > lastSegmentEnd) {
          console.log("DEBUG: After last segment - showing last subtitle");
          const startIndex = Math.max(0, lastIndex - linesToShow + 1);
          const endIndex = lastIndex;
          const combinedLines = subtitles.list.slice(startIndex, endIndex + 1).join("\n");

          setVideoSubtitles((c) => ({
            ...c,
            index: lastIndex,
            currentLine: combinedLines
          }));
        } else {
          console.log("DEBUG: In a gap between segments - showing empty subtitle");
          setVideoSubtitles((c) => ({
            ...c,
            index: lastIndex,
            currentLine: ""
          }));
        }
        return;
      }

      // 3) fraction of total transcribed duration
      const fraction = elapsedInSegments / totalSegmentDuration;
      console.log(`DEBUG: Progress fraction: ${fraction.toFixed(4)}, elapsed: ${elapsedInSegments.toFixed(2)}s/${totalSegmentDuration.toFixed(2)}s`);

      // 4) map to subtitle index
      const newIndex = Math.floor(fraction * subtitles.list.length);
      console.log(`DEBUG: Mapped to subtitle index: ${newIndex}/${subtitleMax - 1}`);

      if (newIndex >= 0 && newIndex < subtitles.list.length) {
        // Get multiple lines centered around the current index
        const startIndex = Math.max(0, newIndex - Math.floor(linesToShow / 2));
        const endIndex = Math.min(subtitleMax - 1, startIndex + linesToShow - 1);
        console.log(`DEBUG: Showing subtitles from index ${startIndex} to ${endIndex}`);

        // Join multiple subtitle lines
        const combinedLines = subtitles.list.slice(startIndex, endIndex + 1).join("\n");

        setVideoSubtitles((c) => ({
          ...c,
          index: newIndex,
          currentLine: combinedLines,
        }));
      }
    } else {
      console.log("DEBUG: No audio segments - using standard video timing");
      // Standard video without audio segments
      let index = Math.round(videoTime * indexMultiplier);

      // Safety bounds
      if (index < 0) index = 0;
      if (index >= subtitleMax) index = subtitleMax - 1;
      console.log(`DEBUG: Standard timing index: ${index}/${subtitleMax - 1}`);

      // Get multiple lines centered around the current index
      const startIndex = Math.max(0, index - Math.floor(linesToShow / 2));
      const endIndex = Math.min(subtitleMax - 1, startIndex + linesToShow - 1);
      console.log(`DEBUG: Showing subtitles from index ${startIndex} to ${endIndex}`);

      // Join multiple subtitle lines
      const combinedLines = subtitles.list.slice(startIndex, endIndex + 1).join("\n");

      setVideoSubtitles((c) => ({
        ...c,
        index,
        currentLine: combinedLines
      }));
    }
  }

  const onLikeVideoClick = useCallback(() => {
    AnswerServiceInstance?.likeAnswer(
      user.id + "",
      selectedAnswer?.id + ""
    ).then((data: interfaces.LikeInterface) => {
      const updatedAnswer: interfaces.AnswerInterface = {
        ...selectedAnswer,
        liked: data?.enabled,
        likes: !data?.enabled
          ? (selectedAnswer?.likes || 1) - 1
          : (selectedAnswer?.likes || 1) + 1,
      } as interfaces.AnswerInterface;

      handleVideoUpdate(updatedAnswer);
    });
  }, [handleVideoUpdate, selectedAnswer, user]);

  const handleVideoBannerClick = useCallback(() => {
    TrackerServiceInstance?.trackEvent("clickDonate", {
      event_category: "Donate",
    });

    const videoBannerURL = client?.videoBannerURL;

    if (videoBannerURL) window.open(videoBannerURL, "_blank", "noreferrer");
  }, [client]);

  const onShareClick = useCallback(
    (channel: string) => {
      const host: string = window.location.hostname,
        clientPathName: string = window.location.pathname,
        shareURLBase: string = host === 'localhost' ? `http://localhost:3000${clientPathName}?aId=` : (
          host === 'staging.repd.us' ? `https://staging.repd.us${clientPathName}?aId=` : (
            `https://share.repd.us/og${clientPathName}/`
          )
        ),
        answerURL: string = `${shareURLBase}${selectedAnswer?.id}`,
        encodedAnswerURL: string = encodeURIComponent(answerURL),
        shareText: string = encodeURIComponent(selectedAnswer?.question?.text + '');

      if (channel === 'copy')
        TrackerServiceInstance?.trackEvent('copyLink', { event_category: 'Answers', answerId: selectedAnswer?.id });
      else
        TrackerServiceInstance?.trackEvent('shareVideo', { event_category: 'Answers', answerId: selectedAnswer?.id });

      switch (channel) {
        case 'twitter': {
          let shareURL = `https://twitter.com/intent/tweet?url=${encodedAnswerURL}&text=${shareText}`;

          if (shareURL.length > 280) {
            shareURL = `https://twitter.com/intent/tweet?url=${encodedAnswerURL}`;
          }

          window.open(shareURL, '_blank', 'noreferrer');

          break;
        }
        case 'facebook': {
          const shareURL = `https://www.facebook.com/sharer/sharer.php?u=${encodedAnswerURL}&t=${shareText}`;

          window.open(shareURL, '_blank', 'noreferrer');

          break;
        }
        case 'email': {
          const emailSubject = encodeURIComponent(`Candidate ${client.name}: ${selectedAnswer?.question?.text + ''}`);
          const emailBody =
            answerURL +
            '%0D%0A' +
            encodeURIComponent(
              `Check out ${client.firstName}'s answer to the question: ${selectedAnswer?.question?.text + ''}`
            );
          const shareURL = `mailto:?subject=${emailSubject}&body=${emailBody}`;

          window.open(shareURL, '_blank', 'noreferrer');

          break;
        }
        default: {
          const confirmText: string = `Please enable clipboard permissions, \nor copy this link directly: ${answerURL}`

          if (navigator?.permissions?.query)
            navigator.permissions.query({ name: 'clipboard-read', allowWithoutGesture: false } as any).then(permissionStatus => {
              // Will be 'granted', 'denied' or 'prompt':
              console.log(permissionStatus.state);
            })

          if (window.navigator?.clipboard?.writeText)
            window.navigator.clipboard.writeText(answerURL).then(
              () => { },
              () => toast.error(confirmText, { autoClose: false, draggable: false })
            );
          else
            toast.error(confirmText, { autoClose: false, draggable: false })

          break;
        }
      }
    },
    [client, selectedAnswer]
  );

  const onFeedbackClick = useCallback(
    (mark: string) => {
      TrackerServiceInstance?.trackEvent("videoFeedbackSelected", {
        event_category: "Answers",
        videoFeedbackAmount: mark,
        answerId: selectedAnswer?.id,
      }).then(() => {
        setVideoState((cur) => ({ ...cur, isFeedbackSubmitted: true }));
      });
    },
    [selectedAnswer]
  );

  const onReplayClick = useCallback(() => {
    if (!videoRef.current) return;

    setVideoState((cur) => ({
      ...cur,
      isFeedbackShown: false,
      ended: false,
      started: true,
      paused: false,
    }));

    videoRef.current.currentTime = 0;

    setTimeout(() => {
      if (videoRef.current) videoRef.current.play();
    }, 10);
  }, []);

  const DonationBanner = () => {
    const defaultDonationText: string = `${t("Support")} ${client?.name} ${t(
      "now by making a donation to the campaign"
    )}!`;

    const customStyles = {
      textColor: client?.plusAskPillColour
        ? { color: client.plusAskPillColour }
        : {},
      backgroundColor: client?.plusAskPillColour
        ? {
          backgroundColor: client.plusAskPillColour,
          borderColor: client.plusAskPillColour,
        }
        : {},
    };

    // return (
    //   <div style={customStyles.textColor} className={classes.donationBanner}>
    //     <div className={classes.donationInnerWrapper}>
    //       <Icons iconType="bullhorn" iconClass={classes.donationIcon} />
    //       <span className={classes.donationText}>
    //         {translateCustomText("donateText", defaultDonationText)}
    //       </span>
    //     </div>

    //     <button
    //       type="button"
    //       className={cn(ButtonClasses.Button, classes.donateNowButton)}
    //       style={customStyles.backgroundColor}
    //       onClick={() => handleDonateClick()}
    //     >
    //       {translateCustomText("donateCtaText", t("Donate Now"))}
    //     </button>
    //   </div>
    // );

    return (
      <div className={classes.learnMoreContainer}>
        <div className={classes.learnMoreTextContainer}>
          <img className={classes.cityIcon} src={cityIcon} alt="City Icon" />
          <p style={customStyles.textColor} className={classes.learnMoreText}>
            {translateCustomText("donateText", defaultDonationText)}
            {/* {t("Learn more about")} {client?.name} */}
          </p>
        </div>
        <button
          onClick={() => handleVideoBannerClick()}
          style={customStyles.backgroundColor}
          className={classes.learnMoreButton}>
          {translateCustomText("donateCtaText", t("Donate Now"))}
          {/* {t("Learn more")} */}
          <img className={classes.learnMoreIcon} src={arrowIcon} />
        </button>
      </div>
    )
  };

  function getVideoFeedbackTemplate() {
    return (
      <div
        className={cn(
          classes.feedbackBlock,
          videoState.isFeedbackShown && videoState.ended && classes.activated
        )}
      >
        <div className={classes.replayVideo} onClick={() => onReplayClick()}>
          <Icons iconType="redo" iconClass={classes.replayIcon} />
        </div>

        {videoState.isFeedbackSubmitted ? (
          <div className={classes.thankYouText}>Thanks for your Feedback!</div>
        ) : (
          <>
            <div className={classes.leaveFeedbackText}>
              {translateCustomText(
                "postVideoSurveyQuestion",
                t(
                  client.textOptions?.postVideoSurveyQuestion ||
                  `Does this answer make you more likely to support ${client.name}?`
                )
              )}
            </div>

            <div className={classes.feedbackMarkBlock}>
              <div className={classes.feedbackValues}>
                <span onClick={() => onFeedbackClick("1")}>
                  <img src={thumbsDown} alt="thumbs down" />
                </span>
                {/* <span onClick={() => onFeedbackClick("2")}>2</span>
                <span onClick={() => onFeedbackClick("3")}>3</span>
                <span onClick={() => onFeedbackClick("4")}>4</span> */}
                <span onClick={() => onFeedbackClick("5")}>
                  <img src={thumbsUp} alt="thumbs up" />
                </span>
              </div>

              <div className={classes.feedbackExplanation}>
                <span>
                  {translateCustomText(
                    "postVideoSurveyLessLikely",
                    client.textOptions?.postVideoSurveyLessLikely ||
                    t("Less Likely")
                  )}
                </span>
                {/* <span>
                  {translateCustomText(
                    "postVideoSurveyNeutral",
                    client.textOptions?.postVideoSurveyNeutral || t("Neutral")
                  )}
                </span> */}
                <span>
                  {translateCustomText(
                    "postVideoSurveyMoreLikely",
                    client.textOptions?.postVideoSurveyMoreLikely ||
                    t("More Likely")
                  )}
                </span>
              </div>
            </div>
          </>
        )}
      </div>
    );
  }

  function getMetaInfoTemplate() {
    return (
      <>
        {(showAltMetaInfo || showMetaData) && (
          <div className={classes.title}>
            <IconAvatar
              categoryIcon={selectedAnswer?.question?.category}
              wrapperClass={classes.iconWrapper}
              iconClass={classes.iconInner}
            />
            <div className={classes.titleRowContainer}>
              <div className={classes.titleRow}>
                <div className={classes.titleText}>{getQuestionTranslation()}</div>
                <div className={classes.meta}>
                  <div className={classes.user}>
                    <div className={classes.text}>
                      {String(selectedAnswer?.question?.overridingName || selectedAnswer?.question?.user?.firstName || "")} •{" "}
                      {String(selectedAnswer?.question?.user?.zip || "00000")} •{" "}
                      {String(selectedAnswer?.question?.votes || 0)}{" "}
                      {(selectedAnswer?.question?.votes || 0) > 1
                        ? t("votes")
                        : t("vote")}{" "}
                      •{" "}
                      {new Date(selectedAnswer?.createdAt || 0).toLocaleString(
                        "default",
                        { month: "short", day: "numeric" }
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div
                style={{
                  backgroundColor: client?.topBarColour
                }}
                className={cn(classes.shareButtonContainer, showShareMenu ? classes.active : '')}
                onClick={() => setShareMenu(!showShareMenu)}>
                <img className={classes.shareIcon} src={shareIcon} alt="Share" />

                <div className={classes.shareOptions} onMouseOut={() => setShareMenu(false)} onBlur={() => setShareMenu(false)}>
                  {SHAREABLE_LINKS.map(({ key, icon, text }) => (
                    <div key={key} className={classes.shareLink} onClick={() => onShareClick(key)} onMouseOver={() => setShareMenu(true)}>
                      <Icons iconType={icon} iconClass={classes.optionIcon} />
                      {t(text)}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </>
    );
  }

  function getLikeCounterButton() {
    return (
      !hideLikeButton && <div className="likesCounterWrapper">
        <CounterButton
          buttonClass={classes.likesCounter}
          buttonText={selectedAnswer?.liked ? "Liked" : "Like"}
          buttonCount={selectedAnswer?.likes || 1}
          askButtonColor={client?.plusAskPillColour}
          isButtonActive={selectedAnswer?.liked}
          handleClick={() => onLikeVideoClick()}
          hasLikedVideo={true}
        />
      </div>
    )
  }

  useEffect(() => {
    if (videoIsPlaying)
      setVideoState((c) => ({ ...c, isFeedbackShown: false, ended: false }));
  }, [selectedAnswer, videoIsPlaying]);

  useEffect(() => {
    if (!selectedAnswer) return;

    setVideoState((c) => ({
      ...c,
      ended: false,
      started: false,
      paused: false,
      seeked: true,
      seeking: false,
      isDonationBannerShown: false,
      isFeedbackShown: false,
      isFeedbackSubmitted: false,
    }));

    // Force video to reload with new sources
    if (videoRef.current) {
      videoRef.current.load();
    }
  }, [selectedAnswer]);

  useEffect(() => {
    // setSubtitles();
    if (videoIsPaused || !selectedAnswer?.transcription)
      setVideoSubtitles({} as VideoSubtitleStateInterface);
    else setSubtitles();
  }, [
    selectedAnswer?.id,
    selectedAnswer,
    selectedAnswer?.transcription,
    videoIsPaused,
    videoIsPlaying,
    language,
  ]);

  return (
    <>
      <div
        className={cn(
          classes.SelectedAnswer,
          "SelectedAnswer",
          videoState.isFullScreen && classes.fullScreen
        )}
      >
        <div className={classes.controlVideoWrapper}>
          <canvas ref={canvasRef} className={classes.videoBackground}></canvas>

          <video
            id={isMobile ? "mobile_video_player" : "video_player"}
            ref={videoRef}
            className={classes.videoPlayer}
            poster={selectedAnswer && selectedAnswer.imageUrl}
            // src={selectedAnswer?.videoUrls?.mp4 || selectedAnswer?.videoUrl}
            playsInline
            muted={muted}
            onLoadedData={() => {
              if (canvasRef.current) {
                canvasRef.current.width = videoRef.current.videoWidth;
                canvasRef.current.height = videoRef.current.videoHeight;
                if (selectedAnswer) {
                  const previewPoster = new Image();
                  previewPoster.src = selectedAnswer.imageUrl;
                  videoBackgroundContext?.drawImage(previewPoster, 0, 0);
                }
              }
            }}
            onPlay={drawBackground}
            onSeeked={() => {
              videoBackgroundContext?.drawImage(videoRef.current, 0, 0);
            }}
            onTimeUpdate={setSubtitle}
          >
            {selectedAnswer?.videoUrls?.mp4 && (
              <source
                src={selectedAnswer.videoUrls.mp4 || selectedAnswer?.videoUrl}
                type="video/mp4"
              />
            )}
            <source
              src={selectedAnswer?.videoUrl}
            />
            {selectedAnswer?.videoUrls?.webm && (
              <source src={selectedAnswer.videoUrls.webm} type="video/webm" />
            )}
            {selectedAnswer?.videoUrls?.ogv && (
              <source src={selectedAnswer.videoUrls.ogv} type="video/ogv" />
            )}
          </video>

          <FontAwesomeIcon
            icon={faSpinner}
            className={cn(
              classes.seekingSpinner,
              videoState.seeking && classes.seeking
            )}
            pulse
          />

          {subtitles.enabled && subtitles.currentLine !== "" && (
            <div className={classes.subtitlesContainer}>
              <div className={classes.subtitleLine}>{subtitles.currentLine}</div>
            </div>
          )}

          {/* {(!showAltMetaInfo || videoState.isFullScreen) ? getMetaInfoTemplate() : null} */}
          {/* {getLikeCounterButton()} */}

          <VideoControl
            selectedAnswer={selectedAnswer}
            videoRef={videoRef}
            videoState={videoState}
            muted={muted}
            setMuted={setMuted}
            updateVideoState={(v) => setVideoState((cur) => ({ ...cur, ...v }))}
            onFullScreen={(v) =>
              setVideoState((cur) => ({ ...cur, isFullScreen: v }))
            }
          />

          {getVideoFeedbackTemplate()}
        </div>

        <div className={cn(classes.mobileMetaInfo, "mobileMetaInfo")}>
          {getMetaInfoTemplate()}
          {/* {getLikeCounterButton()} */}
        </div>

        {getVideoFeedbackTemplate()}
      </div>

      <div className={classes.hideMobile}>{getMetaInfoTemplate()}</div>

      {client?.donationURL && <DonationBanner />}
    </>
  );
}
