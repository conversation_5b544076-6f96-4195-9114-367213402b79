@import 'styles/variables';

.AnswerItem {
    display: flex;
    margin-bottom: 20px;
    padding: 10px 10px 20px 10px;
    border-bottom: 1px solid $grey;
    font-size: 20px;

    .thumbnail {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 20px;
        width: 100%;
        height: 250px;
        max-width: 400px;
        background: black;
        border-radius: 10px;
        color: $grey;
        font-size: 65px;
        text-align: center;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;

        video {
            max-width: 100%;
            max-height: 100%;
            border-radius: 10px;
        }

        button {
            position: absolute;
            width: 100%;
            height: 100%;
            color: transparent;
            background-color: transparent;
            border: none;
            border-radius: 10px;

            &:hover {
                color: white;
                background-color: black;
                opacity: 0.5;
            }

            svg {
                width: 30%;
                height: auto;
            }
        }
    }

    .meta {
        position: relative;
        display: flex;
        justify-content: start;
        align-items: center;
        align-content: flex-start;
        flex-wrap: wrap;
        width: calc(100% - 300px - 20px);
        width: 100%;
    }

    &:hover {
        .pinButton {
            opacity: 0.95;

            &.unpinned {
                opacity: 1;
                background-color: white;
                border: 1px solid $light-grey;

                &:hover {
                    opacity: 0.5;
                }
            }
        }
    }

    .pinButton {
        height: 2.5rem; // h-10
        width: 2.5rem; // w-10
        margin-left: 0.5rem; // ml-2
        display: flex;
        background-color: $dark-blue;
        border: 1px solid $dark-blue;
        transition-duration: 200ms;
        border-radius: 9999px; // rounded-full
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &.unpinned {
            background-color: #fff;
            border: 1px solid $grey;
        }

        img {
            height: 1.25rem; // h-5
            width: 1.25rem; // w-5
        }
    }

    .user {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .name {
            margin-left: 10px;
            color: $dark-grey;
            font-size: 16px;
        }
    }

    .buttonBar {
        position: absolute;
        right: 0;
        top: 0;
        display: flex;
        flex-direction: row;
        gap: 20px;
        align-items: stretch;

        button {
            font-size: 18px;
            margin-left: 10px;

            &:first-child {
                margin-left: 0;
            }
        }

        .checkBoxButton {
            padding: 5px 15px;
            border-radius: 40px;
            border: 1px solid #d8d8d8;
            color: #34538d;
            background-color: white;

            input {
                margin-right: 10px;
                padding: 5px;
            }
        }

        .dropdownBar {
            padding-top: 10px;
            position: absolute;
            right: 0;
            top: 50px;
            z-index: 100;
            border-radius: 10px;
            background: white;
            box-shadow: 5px 5px 20px 5px $transparent-black-low;
            transition: opacity 5s, height 5s;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            button,
            div {
                margin-bottom: 10px;
                margin-left: 10px;
                margin-right: 10px;
                font-size: 16px;
                min-width: 200px;
                text-align: center;
            }
        }
    }

    .question {
        display: flex;
        flex-wrap: wrap;
        width: 100%;

        .questionText {
            width: 100%;
            padding-top: 15px;
            font-size: 24px;
            font-weight: 300;
        }

        .attributes {
            display: flex;
            width: 100%;
            padding-top: 15px;
            font-size: 16px;
            color: $dark-grey;
            justify-content: space-between;
        }
    }

    &:last-child {
        border: none;
        padding: 0;
    }

}

@media (max-width : 640px) {
    .AnswerItem {
        flex-wrap: wrap;

        .thumbnail {
            margin: auto;
            width: 100%;
        }

        .meta {
            margin-top: 10px;
        }

        .buttonBar {
            margin-top: 10px;
            position: relative;
            gap: 0;
        }
    }
}