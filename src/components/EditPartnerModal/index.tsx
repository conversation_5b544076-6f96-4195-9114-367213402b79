import React, { useState, useEffect } from 'react';
import Modal from 'shared/Modal';
import Button from 'shared/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import cn from 'classnames';
import { ClientInterface } from 'interfaces';
import { PartnersService, PartnerResponse, ApiKey, RawApiKey } from 'services/partners.service';
import { toast } from 'react-toastify';

import classes from './EditPartnerModal.module.scss';

interface PartnerInterface {
  id: string;
  name: string;
  features: string[];
  clientIds: string[];
  enabled: boolean;
  apiEnabled: boolean;
  apiKeyIds: string[];
  apiKeys: ApiKey[];
  rawApiKeys: RawApiKey[];
  createdAt: string;
  updatedAt: string;
}

interface EditPartnerModalProps {
  isOpen: boolean;
  handleClose: () => void;
  partner: PartnerInterface | null;
  onSave: (partner: PartnerR<PERSON>ponse, newClientApiKeys?: RawApiKey[]) => void;
  clients: ClientInterface[];
  partnersService: PartnersService;
}

interface MultiSelectProps {
  options: { id: string; name: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder: string;
  showIds?: boolean;
}

const MultiSelect: React.FC<MultiSelectProps> = ({ options, selected, onChange, placeholder, showIds = false }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOption = (optionId: string) => {
    const newSelected = selected.includes(optionId)
      ? selected.filter(item => item !== optionId)
      : [...selected, optionId];
    onChange(newSelected);
  };

  const getDisplayValue = () => {
    if (selected.length === 0) return placeholder;
    if (selected.length === 1) {
      const option = options.find(opt => opt.id === selected[0]);
      if (!option) return selected[0];
      return showIds ? `${option.name} - ${option.id}` : option.name;
    }
    return `${selected.length} selected`;
  };

  const getOptionDisplayText = (option: { id: string; name: string }) => {
    return showIds ? `${option.name} - ${option.id}` : option.name;
  };

  return (
    <div className={classes.multiSelect}>
      <div
        className={classes.multiSelectTrigger}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{getDisplayValue()}</span>
        <FontAwesomeIcon icon={['fas', 'chevron-down']} className={cn(classes.chevron, isOpen && classes.open)} />
      </div>
      {isOpen && (
        <div className={classes.multiSelectDropdown}>
          {options.map(option => (
            <div
              key={option.id}
              className={cn(classes.multiSelectOption, selected.includes(option.id) && classes.selected)}
              onClick={() => toggleOption(option.id)}
            >
              <input
                type="checkbox"
                checked={selected.includes(option.id)}
                onChange={() => { }} // Handled by parent onClick
                className={classes.checkbox}
              />
              <span>{getOptionDisplayText(option)}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default function EditPartnerModal({
  isOpen,
  handleClose,
  partner,
  onSave,
  clients,
  partnersService,
}: EditPartnerModalProps) {
  const [name, setName] = useState('');
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);
  const [apiEnabled, setApiEnabled] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const featureOptions = [
    { id: 'video_library', name: 'Video Library' }
  ];

  const clientOptions = clients.map(client => ({
    id: client.id,
    name: client.name
  }));

  // Populate form when partner changes
  useEffect(() => {
    if (partner) {
      setName(partner.name);
      setSelectedFeatures(partner.features);
      // Use the actual client IDs stored in the partner
      setSelectedClients(partner.clientIds || []);
      setApiEnabled(partner.apiEnabled);
    }
  }, [partner]);

  const handleSave = async () => {
    if (partner && name.trim() && selectedFeatures.length > 0 && selectedClients.length > 0) {
      setIsLoading(true);

      try {
        // Detect newly added clients
        const originalClientIds = partner.clientIds || [];
        const newlyAddedClientIds = selectedClients.filter(clientId => !originalClientIds.includes(clientId));

        const updateData = {
          name: name.trim(),
          features: selectedFeatures,
          clientIds: selectedClients,
          apiEnabled: apiEnabled
        };

        const updatedPartner = await partnersService.updatePartner(partner.id, updateData);

        // Extract API keys for newly added clients
        const newClientApiKeys = updatedPartner.rawApiKeys?.filter(rawApiKey =>
          newlyAddedClientIds.includes(rawApiKey.clientId)
        ) || [];

        // Call onSave with the updated partner data and new client API keys
        onSave(updatedPartner, newClientApiKeys.length > 0 ? newClientApiKeys : undefined);

        toast.success('Partner updated successfully!');
        handleClose();
      } catch (error) {
        console.error('Error updating partner:', error);
        toast.error('Failed to update partner. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCloseModal = () => {
    handleClose();
  };

  if (!isOpen || !partner) return null;

  return (
    <Modal contentWrapperStyle={{ maxWidth: "520px" }}>
      <div className={classes.editPartnerModal}>
        <div className={classes.header}>
          <h2 className={classes.title}>Edit Partner</h2>
          <button className={classes.closeButton} onClick={handleCloseModal}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <div className={classes.content}>
          <div className={classes.inputGroup}>
            <label className={classes.label}>Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className={classes.input}
              placeholder="Enter partner name"
            />
          </div>

          <div className={classes.inputGroup}>
            <label className={classes.label}>Features</label>
            <MultiSelect
              options={featureOptions}
              selected={selectedFeatures}
              onChange={setSelectedFeatures}
              placeholder="Select features"
              showIds={false}
            />
          </div>

          <div className={classes.inputGroup}>
            <label className={classes.label}>Clients</label>
            <MultiSelect
              options={clientOptions}
              selected={selectedClients}
              onChange={setSelectedClients}
              placeholder="Select clients"
              showIds={true}
            />
          </div>

          <div className={classes.inputGroup}>
            <label className={classes.checkboxLabel}>
              <input
                type="checkbox"
                checked={apiEnabled}
                onChange={(e) => setApiEnabled(e.target.checked)}
                className={classes.enabledCheckbox}
              />
              <span>API Enabled</span>
            </label>
          </div>

          <div className={classes.buttonContainer}>
            <Button
              text={isLoading ? "Updating..." : "Save"}
              callback={handleSave}
              customClass={(!name.trim() || selectedFeatures.length === 0 || selectedClients.length === 0 || isLoading) ? "disabled" : "primary"}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
}
