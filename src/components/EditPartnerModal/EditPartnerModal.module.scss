@import 'styles/variables';

.editPartnerModal {
  background: white;
  border-radius: 8px;
  padding: 0;
  width: 100%;
  max-width: 520px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: $dark-blue;
  margin: 0;
  text-align: center;
  flex: 1;
}

.closeButton {
  background: none;
  border: none;
  color: $dark-blue;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    color: $blue;
  }
}

.content {
  padding: 0 24px 24px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.inputGroup {
  width: 100%;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.label {
  font-weight: 600;
  color: $dark-blue;
  margin-bottom: 8px;
  font-size: 16px;
  align-self: flex-start;
}

.input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid $grey;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: $dark-blue;
  }

  &::placeholder {
    color: #999;
  }
}

.multiSelect {
  width: 100%;
  position: relative;
}

.multiSelectTrigger {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid $grey;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: $dark-blue;
  }

  span {
    color: #333;
  }
}

.chevron {
  transition: transform 0.2s ease;
  color: #666;

  &.open {
    transform: rotate(180deg);
  }
}

.multiSelectDropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid $grey;
  border-top: none;
  border-radius: 0 0 6px 6px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.multiSelectOption {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &.selected {
    background-color: #e3f2fd;
  }

  span {
    margin-left: 8px;
    font-size: 16px;
  }
}

.checkbox {
  margin: 0;
  cursor: pointer;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: $dark-blue;
  font-size: 16px;
  cursor: pointer;
  align-self: flex-start;

  span {
    margin-left: 8px;
  }
}

.enabledCheckbox {
  margin: 0;
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.buttonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

@media (max-width: 640px) {
  .editPartnerModal {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }

  .header {
    padding: 20px 20px 0 20px;
  }

  .content {
    padding: 0 20px 20px 20px;
  }

  .title {
    font-size: 20px;
  }
}
