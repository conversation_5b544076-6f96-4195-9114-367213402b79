import React, { useEffect, useState } from "react";
import classesFile from "../../../Answer/Answer.module.scss";
import Button from "../../../../shared/Button";
import { useServiceContext } from "../../../../services/ServiceProvider";
import { useVideoUploadContext, VideoStepStatus } from "../VideoUploadProvider";
import UploadThumbnail from "../../UploadThumbnail";
import Thumbnail from "../../VideoCapture/Thumbnail";

const classes: any = classesFile;

export const thumbnailCount = 15;

const ThumbnailSelectionStep: React.FC = () => {
  const { status, handleClose, setAnswer, answer } = useVideoUploadContext();
  const { answersService } = useServiceContext();
  const [selectedThumbnail, setSelectedThumbnail] = useState<string | null>(
    null
  );

  const [availableThumbnailUrls, setAvailableThumbnailUrls] = useState<
    string[]
  >([]);

  useEffect(() => {
    if (!answer) {
      return;
    }
    function getThumbnailUrl(n: number) {
      return `https://files.repd.us/thumbnails/${answer?.videoUrl.split(/video_parts\/|raw\//)[1].split("/")[0]
        }-${n}.png`;
    }

    const thumbnailUrls = Array(10)
      .fill(0)
      .map((_, index) => getThumbnailUrl(index));

    const checkUrls = async () => {
      const results: string[] = (
        await Promise.all(
          thumbnailUrls.map(async (url) => {
            try {
              const response = await fetch(
                url,
                // `${answersService?.base}/video-proxy?url=${encodeURIComponent(
                //   url
                // )}`,
                { method: "HEAD" }
              );
              return response.ok ? url : "";
            } catch {
              return "";
            }
          })
        )
      ).filter((item) => !!item);
      setAvailableThumbnailUrls(results);
      if (results.length) {
        setSelectedThumbnail(results[0]);
      }
    };

    checkUrls();
  }, [answer]);
  const handleThumbnailClick = (thumbnail: string) => {
    setSelectedThumbnail(thumbnail);
  };

  async function handlePublish() {
    if (!selectedThumbnail) {
      return;
    }
    const publishedAnswer: any = { ...answer, imageUrl: selectedThumbnail };
    answersService?.publishAnswer(publishedAnswer).then(() => {
      setAnswer(publishedAnswer);
    });
  }

  if (status !== VideoStepStatus.thumbnail) {
    return null;
  }

  return (
    <div className="p-4">
      {selectedThumbnail ? (
        <div className="mb-4 flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 rounded-lg bg-gray-100 py-4">
          <img
            src={selectedThumbnail}
            alt="Video Thumbnail"
            className="h-64 mx-auto"
          />
        </div>
      ) : (
        <div className="mb-4 flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 rounded-lg bg-gray-100 p-4">
          <div className="text-center text-gray-500">
            <p>{availableThumbnailUrls.length > 0 ? 'Select a thumbnail from the list below' : 'Upload a thumbnail'}</p>
          </div>
        </div>
      )}
      <div className="flex mb-4 gap-4 overflow-x-scroll">
        {availableThumbnailUrls.map((url, index) => (
          <div>
            <Thumbnail
              selectedThumbnail={selectedThumbnail}
              url={url}
              handleThumbnailClick={handleThumbnailClick}
            />
          </div>
        ))}
      </div>
      <UploadThumbnail
        setSelectedThumbnail={setSelectedThumbnail}
        selectedThumbnail={selectedThumbnail}
      />
      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
        {selectedThumbnail && (
          <Button text="Publish" callback={handlePublish} />
        )}
      </div>
    </div>
  );
};

export default ThumbnailSelectionStep;
