import {
  useVideoRecordContext,
  VideoRecordStepStatus,
} from "../VideoRecordProvider";
import Button from "../../../../shared/Button";
import React from "react";
import classesFile from "../../../Answer/Answer.module.scss";
import { useAuthContext } from "../../../../services/AuthProvider";

const classes: any = classesFile;

export default function PublishedStep() {
  const { handleClose, status, answer } = useVideoRecordContext();
  const { user } = useAuthContext();

  if (status !== VideoRecordStepStatus.publish) {
    return null;
  }

  const thumbnailUrl = answer?.imageUrl;
  const isGuestAdmin = user?.accessLevel === "guest admin";
  const title = isGuestAdmin ? "Video Submitted!" : "Video Published!";
  const message = isGuestAdmin
    ? "Congratulations! Your video has been submitted successfully. It will be reviewed before being published."
    : "Congratulations! Your video has been published successfully. It's now available for everyone to view.";

  return (
    <>
      <div className="bg-white justify-between gap-8">
        <div className=" items-center mb-4">
          <h2 className="text-xl font-semibold">{title}</h2>
          <p>
            {message}
          </p>
        </div>
        <div className="mb-4 bg-gray-200 flex justify-center">
          <img
            src={thumbnailUrl}
            alt="Video Thumbnail"
            className="h-64 mx-auto"
          />
        </div>
      </div>

      <div className={classes.buttonWrapper}>
        <Button text="Close" callback={handleClose} />
      </div>
    </>
  );
}
