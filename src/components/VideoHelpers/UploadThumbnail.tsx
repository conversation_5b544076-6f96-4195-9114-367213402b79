import React, { useState, useRef } from "react";
import { useServiceContext } from "../../services/ServiceProvider";
import Thumbnail from "./VideoCapture/Thumbnail";
import ThumbnailCaptureOptions from "./ThumbnailCaptureOptions";

export default function UploadThumbnail({
  setSelectedThumbnail,
  selectedThumbnail,
}: any) {
  const [file, setFile] = useState<any>(null);
  const [captureOption, setCaptureOption] = useState<"upload" | "camera" | null>(null);
  const { fileService, adminStatsService } = useServiceContext();
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileChange = async (event: any) => {
    const selectedFile = event.target.files[0];
    if (selectedFile) {
      const fileURL = URL.createObjectURL(selectedFile);
      const blob = await fetch(fileURL).then((res) => res.blob());
      const formData = new FormData();
      formData.append("file", blob, "thumbnail.png");
      fileService
        ?.uploadImage(formData, () => { })
        .then((response: any) => {
          setFile(response.url);
          setSelectedThumbnail(response.url);
          setCaptureOption(null);
        });
    }
  };

  const handleCameraCapture = async (imageDataUrl: string) => {
    const blob = await fetch(imageDataUrl).then((res) => res.blob());
    const formData = new FormData();
    formData.append("file", blob, "thumbnail.png");
    fileService
      ?.uploadImage(formData, () => { })
      .then((response: any) => {
        setFile(response.url);
        setSelectedThumbnail(response.url);
        setCaptureOption(null);
      });
  };

  const handleUploadClick = () => {
    adminStatsService?.trackEvent('ThumbnailSelection', 'select_upload_photo');
    fileInputRef.current?.click();
  };

  const handleTakePhotoClick = () => {
    setCaptureOption("camera");
    adminStatsService?.trackEvent('ThumbnailSelection', 'select_take_photo');
  };

  if (captureOption === "camera") {
    return (
      <ThumbnailCaptureOptions
        captureOption={captureOption}
        setCaptureOption={setCaptureOption}
        onFileUpload={handleFileChange}
        onCameraCapture={handleCameraCapture}
      />
    );
  }

  return (
    <div className="flex flex-col items-center justify-center w-full h-full bg-gray-100 p-6">
      <h2 className="text-2xl font-bold text-gray-800 mb-6">
        Choose an Option
      </h2>
      <div className="flex space-x-4">
        <button
          onClick={handleUploadClick}
          className="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
        >
          Upload Photo
        </button>
        <button
          onClick={handleTakePhotoClick}
          className="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300"
        >
          Take Photo
        </button>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        className="hidden"
        onChange={handleFileChange}
      />

      {/* Show selected thumbnail if available */}
      {file && (
        <div className="mt-6">
          <Thumbnail
            selectedThumbnail={selectedThumbnail}
            url={file}
            handleThumbnailClick={() => setSelectedThumbnail(file)}
          />
        </div>
      )}
    </div>
  );
}
