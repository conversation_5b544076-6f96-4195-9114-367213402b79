@import 'styles/variables';

.manageApiKeysModal {
  background: white;
  border-radius: 8px;
  padding: 0;
  width: 100%;
  max-width: 600px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: $dark-blue;
  margin: 0;
  text-align: center;
  flex: 1;
}

.closeButton {
  background: none;
  border: none;
  color: $dark-blue;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    color: $blue;
  }
}

.content {
  padding: 0 24px 24px 24px;
  display: flex;
  flex-direction: column;
}

.noKeysText {
  text-align: center;
  color: #666;
  font-size: 16px;
  margin: 40px 0;
}

.keysList {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.keyItem {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;

  &:hover {
    border-color: #ccc;
  }
}

.keyInfo {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.keyHeader {
  display: flex;
  align-items: center;
  gap: 12px;
}

.clientId {
  font-weight: 600;
  color: $dark-blue;
  font-size: 14px;
}

.missingBadge {
  background-color: #ff9800;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.keySample {
  code {
    background-color: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #333;
  }
}

.noKeyText {
  color: #666;
  font-style: italic;
  font-size: 14px;
  margin: 8px 0;
}

.keyMeta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #666;

  span {
    display: flex;
    align-items: center;
  }
}

.keyActions {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 120px;
}



.buttonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 640px) {
  .manageApiKeysModal {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }

  .header {
    padding: 20px 20px 0 20px;
  }

  .content {
    padding: 0 20px 20px 20px;
  }

  .title {
    font-size: 20px;
  }

  .keyItem {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .keyActions {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }



  .keyMeta {
    flex-direction: column;
    gap: 4px;
  }
}