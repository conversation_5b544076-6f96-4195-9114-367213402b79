import React, { useState } from 'react';
import Modal from 'shared/Modal';
import Button from 'shared/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faTrash, faSync } from '@fortawesome/free-solid-svg-icons';
import { PartnersService, ApiKey } from 'services/partners.service';
import { ClientInterface } from 'interfaces';
import { toast } from 'react-toastify';
import cn from 'classnames';

import classes from './ManageApiKeysModal.module.scss';

interface ClientWithApiKey {
  clientId: string;
  clientName: string;
  apiKey?: ApiKey;
}

interface ManageApiKeysModalProps {
  isOpen: boolean;
  handleClose: () => void;
  partnerId: string;
  partnerName: string;
  clientIds: string[];
  apiKeys: ApiKey[];
  clients: ClientInterface[];
  partnersService: PartnersService;
  onApiKeysUpdated: (updatedApiKeys: ApiKey[]) => void;
  onShowNewApiKey?: (newApiKey: { clientId: string; rawKey: string; keySample: string }) => void;
}

export default function ManageApiKeysModal({
  isOpen,
  handleClose,
  partnerId,
  partnerName,
  clientIds,
  apiKeys,
  clients,
  partnersService,
  onApiKeysUpdated,
  onShowNewApiKey,
}: ManageApiKeysModalProps) {
  const [loadingStates, setLoadingStates] = useState<{ [clientId: string]: 'revoking' | 'regenerating' | null }>({});

  // Create a combined list of clients with their API keys
  const clientsWithApiKeys: ClientWithApiKey[] = clientIds.map(clientId => {
    const client = clients.find(c => c.id === clientId);
    const apiKey = apiKeys.find(key => key.clientId === clientId);

    return {
      clientId,
      clientName: client?.name || `Client ${clientId}`,
      apiKey
    };
  });

  const handleRevokeKey = async (clientId: string) => {
    setLoadingStates(prev => ({ ...prev, [clientId]: 'revoking' }));

    try {
      await partnersService.revokeApiKey(partnerId, clientId);

      // Remove the API key from the list since it's now deleted
      const updatedApiKeys = apiKeys.filter(key => key.clientId !== clientId);
      onApiKeysUpdated(updatedApiKeys);

      toast.success('API key revoked successfully!');
    } catch (error) {
      console.error('Error revoking API key:', error);
      toast.error('Failed to revoke API key. Please try again.');
    } finally {
      setLoadingStates(prev => ({ ...prev, [clientId]: null }));
    }
  };

  const handleRegenerateKey = async (clientId: string) => {
    setLoadingStates(prev => ({ ...prev, [clientId]: 'regenerating' }));

    try {
      const response = await partnersService.regenerateApiKey(partnerId, clientId);

      // Update the API keys list with the new key
      const existingKeyIndex = apiKeys.findIndex(key => key.clientId === clientId);
      let updatedApiKeys;

      if (existingKeyIndex >= 0) {
        // Update existing key
        updatedApiKeys = apiKeys.map(key =>
          key.clientId === clientId ? {
            ...key,
            id: response.newApiKey.id,
            keySample: response.newApiKey.keySample,
            updatedAt: new Date().toISOString()
          } : key
        );
      } else {
        // Add new key for client that didn't have one
        updatedApiKeys = [...apiKeys, {
          id: response.newApiKey.id,
          key: '', // We don't store the full key
          keySample: response.newApiKey.keySample,
          clientId: clientId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }];
      }

      onApiKeysUpdated(updatedApiKeys);

      // Show the new raw API key to the user
      if (onShowNewApiKey) {
        onShowNewApiKey({
          clientId: response.newApiKey.clientId.toString(),
          rawKey: response.newApiKey.rawKey,
          keySample: response.newApiKey.keySample
        });
      }

      toast.success('API key regenerated successfully!');
    } catch (error) {
      console.error('Error regenerating API key:', error);
      toast.error('Failed to regenerate API key. Please try again.');
    } finally {
      setLoadingStates(prev => ({ ...prev, [clientId]: null }));
    }
  };

  if (!isOpen) return null;

  return (
    <Modal contentWrapperStyle={{ maxWidth: "600px" }}>
      <div className={classes.manageApiKeysModal}>
        <div className={classes.header}>
          <h2 className={classes.title}>Manage API Keys - {partnerName}</h2>
          <button className={classes.closeButton} onClick={handleClose}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <div className={classes.content}>
          {clientsWithApiKeys.length === 0 ? (
            <p className={classes.noKeysText}>No clients found for this partner.</p>
          ) : (
            <div className={classes.keysList}>
              {clientsWithApiKeys.map((clientWithKey) => (
                <div key={clientWithKey.clientId} className={classes.keyItem}>
                  <div className={classes.keyInfo}>
                    <div className={classes.keyHeader}>
                      <span className={classes.clientId}>
                        {clientWithKey.clientName} - {clientWithKey.clientId}
                      </span>
                      {!clientWithKey.apiKey && <span className={classes.missingBadge}>NO KEY</span>}
                    </div>
                    {clientWithKey.apiKey && (
                      <>
                        <div className={classes.keySample}>
                          <code>****{clientWithKey.apiKey.keySample}</code>
                        </div>
                        <div className={classes.keyMeta}>
                          <span>Created: {new Date(clientWithKey.apiKey.createdAt).toLocaleDateString()}</span>
                          <span>Updated: {new Date(clientWithKey.apiKey.updatedAt).toLocaleDateString()}</span>
                        </div>
                      </>
                    )}
                    {!clientWithKey.apiKey && (
                      <div className={classes.noKeyText}>
                        This client does not have an API key
                      </div>
                    )}
                  </div>

                  <div className={classes.keyActions}>
                    {!clientWithKey.apiKey ? (
                      // Client has no API key - show only generate
                      <Button
                        text={loadingStates[clientWithKey.clientId] === 'regenerating' ? 'Generating...' : 'Generate Key'}
                        callback={() => handleRegenerateKey(clientWithKey.clientId)}
                        customClass={loadingStates[clientWithKey.clientId] === 'regenerating' ? 'disabled' : 'primary'}
                        icon={<FontAwesomeIcon icon={faSync} />}
                      />
                    ) : (
                      // Client has API key - show both options
                      <>
                        <Button
                          text={loadingStates[clientWithKey.clientId] === 'regenerating' ? 'Regenerating...' : 'Regenerate'}
                          callback={() => handleRegenerateKey(clientWithKey.clientId)}
                          customClass={loadingStates[clientWithKey.clientId] === 'regenerating' ? 'disabled' : 'secondary'}
                          icon={<FontAwesomeIcon icon={faSync} />}
                        />
                        <Button
                          text={loadingStates[clientWithKey.clientId] === 'revoking' ? 'Revoking...' : 'Revoke'}
                          callback={() => handleRevokeKey(clientWithKey.clientId)}
                          customClass={loadingStates[clientWithKey.clientId] === 'revoking' ? 'disabled' : 'danger'}
                          icon={<FontAwesomeIcon icon={faTrash} />}
                        />
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className={classes.buttonContainer}>
            <Button
              text="Close"
              callback={handleClose}
              customClass="primary"
            />
          </div>
        </div>
      </div>
    </Modal>
  );
}
