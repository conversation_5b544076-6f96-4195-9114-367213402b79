@import 'styles/variables';

.apiKeyModal {
  background: white;
  border-radius: 8px;
  padding: 0;
  width: 100%;
  max-width: 500px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0 24px;
  margin-bottom: 24px;
}

.title {
  font-size: 24px;
  font-weight: 600;
  color: $dark-blue;
  margin: 0;
  text-align: center;
  flex: 1;
}

.closeButton {
  background: none;
  border: none;
  color: $dark-blue;
  font-size: 18px;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    color: $blue;
  }
}

.content {
  padding: 0 24px 24px 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.warningText {
  font-size: 16px;
  color: #d32f2f;
  text-align: center;
  margin: 0 0 24px 0;
  line-height: 1.5;

  strong {
    font-weight: 700;
  }
}

.keySection {
  width: 100%;
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.clientLabel {
  font-size: 14px;
  font-weight: 600;
  color: $dark-blue;
  margin: 0 0 8px 0;
  text-align: left;
}

.keyContainer {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.separator {
  border: none;
  border-top: 1px solid #e0e0e0;
  margin: 16px 0;
  width: 100%;
}

.keyInput {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid $grey;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  background-color: #f8f9fa;
  color: #333;
  cursor: text;

  &:focus {
    outline: none;
    border-color: $dark-blue;
  }

  &::-webkit-input-placeholder {
    color: #999;
  }
}

.copyButton {
  padding: 12px 16px;
  border: 1px solid $grey;
  border-radius: 6px;
  background: white;
  color: $dark-blue;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  min-width: 48px;

  &:hover {
    background-color: $dark-blue;
    color: white;
    border-color: $dark-blue;
  }

  &:active {
    transform: scale(0.98);
  }
}

.buttonContainer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@media (max-width: 640px) {
  .apiKeyModal {
    margin: 20px;
    max-width: calc(100vw - 40px);
  }

  .header {
    padding: 20px 20px 0 20px;
  }

  .content {
    padding: 0 20px 20px 20px;
  }

  .title {
    font-size: 20px;
  }

  .keyInput {
    font-size: 12px;
    padding: 10px 12px;
  }

  .copyButton {
    padding: 10px 12px;
    min-width: 44px;
  }
}