import React from 'react';
import Modal from 'shared/Modal';
import Button from 'shared/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faCopy } from '@fortawesome/free-solid-svg-icons';

import classes from './ApiKeyModal.module.scss';

interface RawApiKey {
  clientId: string;
  rawKey: string;
  apiKeyId: string;
}

interface ApiKeyModalProps {
  isOpen: boolean;
  handleClose: () => void;
  partnerName: string;
  rawApiKeys: RawApiKey[];
  isNewClientKeys?: boolean;
}

export default function ApiKeyModal({
  isOpen,
  handleClose,
  partnerName,
  rawApiKeys,
  isNewClientKeys = false,
}: ApiKeyModalProps) {
  const handleCopyKey = async (apiKey: string) => {
    try {
      await navigator.clipboard.writeText(apiKey);
      // You could add a toast notification here if desired
      console.log('API key copied to clipboard');
    } catch (err) {
      console.error('Failed to copy API key:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = apiKey;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
    }
  };

  if (!isOpen) return null;

  return (
    <Modal contentWrapperStyle={{ maxWidth: "500px" }}>
      <div className={classes.apiKeyModal}>
        <div className={classes.header}>
          <h2 className={classes.title}>
            {partnerName} {isNewClientKeys ? 'New Client API Keys' : 'API Keys'}
          </h2>
          <button className={classes.closeButton} onClick={handleClose}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <div className={classes.content}>
          <p className={classes.warningText}>
            <strong>DO NOT LOSE</strong> the following {rawApiKeys.length === 1 ? 'key' : 'keys'}, {rawApiKeys.length === 1 ? 'it' : 'they'} will only be shown once.
          </p>

          {rawApiKeys.map((rawApiKey, index) => (
            <div key={rawApiKey.apiKeyId} className={classes.keySection}>
              <h4 className={classes.clientLabel}>Client ID: {rawApiKey.clientId}</h4>
              <div className={classes.keyContainer}>
                <input
                  type="text"
                  value={rawApiKey.rawKey}
                  readOnly
                  className={classes.keyInput}
                />
                <button
                  className={classes.copyButton}
                  onClick={() => handleCopyKey(rawApiKey.rawKey)}
                  title={`Copy API key for client ${rawApiKey.clientId}`}
                >
                  <FontAwesomeIcon icon={faCopy} />
                </button>
              </div>
              {index < rawApiKeys.length - 1 && <hr className={classes.separator} />}
            </div>
          ))}

          <div className={classes.buttonContainer}>
            <Button
              text="Close"
              callback={handleClose}
              customClass="primary"
            />
          </div>
        </div>
      </div>
    </Modal>
  );
}
