import React, { useState } from 'react';
import Modal from 'shared/Modal';
import Button from 'shared/Button';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import cn from 'classnames';
import { ClientInterface } from 'interfaces';
import { PartnersService, PartnerResponse } from 'services/partners.service';
import { toast } from 'react-toastify';

import classes from './AddPartnerModal.module.scss';

interface AddPartnerModalProps {
  isOpen: boolean;
  handleClose: () => void;
  onSave: (partner: PartnerResponse) => void;
  clients: ClientInterface[];
  partnersService: PartnersService;
}

interface MultiSelectProps {
  options: { id: string; name: string }[];
  selected: string[];
  onChange: (selected: string[]) => void;
  placeholder: string;
  showIds?: boolean;
}

const MultiSelect: React.FC<MultiSelectProps> = ({ options, selected, onChange, placeholder, showIds = false }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOption = (optionId: string) => {
    const newSelected = selected.includes(optionId)
      ? selected.filter(item => item !== optionId)
      : [...selected, optionId];
    onChange(newSelected);
  };

  const getDisplayValue = () => {
    if (selected.length === 0) return placeholder;
    if (selected.length === 1) {
      const option = options.find(opt => opt.id === selected[0]);
      if (!option) return selected[0];
      return showIds ? `${option.name} - ${option.id}` : option.name;
    }
    return `${selected.length} selected`;
  };

  const getOptionDisplayText = (option: { id: string; name: string }) => {
    return showIds ? `${option.name} - ${option.id}` : option.name;
  };

  return (
    <div className={classes.multiSelect}>
      <div
        className={classes.multiSelectTrigger}
        onClick={() => setIsOpen(!isOpen)}
      >
        <span>{getDisplayValue()}</span>
        <FontAwesomeIcon icon={['fas', 'chevron-down']} className={cn(classes.chevron, isOpen && classes.open)} />
      </div>
      {isOpen && (
        <div className={classes.multiSelectDropdown}>
          {options.map(option => (
            <div
              key={option.id}
              className={cn(classes.multiSelectOption, selected.includes(option.id) && classes.selected)}
              onClick={() => toggleOption(option.id)}
            >
              <input
                type="checkbox"
                checked={selected.includes(option.id)}
                onChange={() => { }} // Handled by parent onClick
                className={classes.checkbox}
              />
              <span>{getOptionDisplayText(option)}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default function AddPartnerModal({
  isOpen,
  handleClose,
  onSave,
  clients,
  partnersService,
}: AddPartnerModalProps) {
  const [name, setName] = useState('');
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [selectedClients, setSelectedClients] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const featureOptions = [
    { id: 'video_library', name: 'Video Library' }
  ];

  const clientOptions = clients.map(client => ({
    id: client.id,
    name: client.name
  }));

  const handleSave = async () => {
    if (name.trim() && selectedFeatures.length > 0 && selectedClients.length > 0) {
      setIsLoading(true);

      try {
        const partnerData = {
          name: name.trim(),
          features: selectedFeatures,
          clientIds: selectedClients
        };

        const createdPartner = await partnersService.createPartner(partnerData);

        // Reset form
        setName('');
        setSelectedFeatures([]);
        setSelectedClients([]);

        // Call onSave with the created partner data
        onSave(createdPartner);

        toast.success('Partner created successfully!');
      } catch (error) {
        console.error('Error creating partner:', error);
        toast.error('Failed to create partner. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleCloseModal = () => {
    handleClose();
    // Reset form
    setName('');
    setSelectedFeatures([]);
    setSelectedClients([]);
  };

  if (!isOpen) return null;

  return (
    <Modal contentWrapperStyle={{ maxWidth: "520px" }}>
      <div className={classes.addPartnerModal}>
        <div className={classes.header}>
          <h2 className={classes.title}>Add Partner</h2>
          <button className={classes.closeButton} onClick={handleCloseModal}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <div className={classes.content}>
          <div className={classes.inputGroup}>
            <label className={classes.label}>Name</label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className={classes.input}
              placeholder="Enter partner name"
            />
          </div>

          <div className={classes.inputGroup}>
            <label className={classes.label}>Features</label>
            <MultiSelect
              options={featureOptions}
              selected={selectedFeatures}
              onChange={setSelectedFeatures}
              placeholder="Select features"
              showIds={false}
            />
          </div>

          <div className={classes.inputGroup}>
            <label className={classes.label}>Clients</label>
            <MultiSelect
              options={clientOptions}
              selected={selectedClients}
              onChange={setSelectedClients}
              placeholder="Select clients"
              showIds={true}
            />
          </div>

          <div className={classes.buttonContainer}>
            <Button
              text={isLoading ? "Creating..." : "Save"}
              callback={handleSave}
              customClass={(!name.trim() || selectedFeatures.length === 0 || selectedClients.length === 0 || isLoading) ? "disabled" : "primary"}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
}
