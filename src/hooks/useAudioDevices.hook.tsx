import { useState, useEffect, useCallback, useMemo } from 'react';

interface AudioDevice {
  deviceId: string;
  label: string;
}

export const useAudioDevices = () => {
  const [audioDevices, setAudioDevices] = useState<AudioDevice[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(
    () => localStorage.getItem('selectedAudioDevice')
  );
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const getAudioDevices = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Try to get user permission with more flexible constraints
      let stream: MediaStream | null = null;
      try {
        stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: true });
      } catch (permissionError) {
        // If video+audio fails, try audio only
        console.log('Video+audio permission failed, trying audio only...');
        try {
          stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        } catch (audioOnlyError) {
          console.error('Audio permission also failed:', audioOnlyError);
          throw audioOnlyError;
        }
      }

      const devices = await navigator.mediaDevices.enumerateDevices();
      const audioInputs = devices
        .filter((device) => device.kind === 'audioinput')
        .map((device) => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(-4)}`,
        }));

      setAudioDevices(audioInputs);

      // Clean up the stream we used for permission
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    } catch (err) {
      const error = err as DOMException;
      if (error.name === 'NotAllowedError') {
        setError('Microphone access denied. Please allow microphone permissions.');
      } else if (error.name === 'NotFoundError') {
        setError('No microphone found. Please connect a microphone.');
      } else {
        setError('Could not access audio devices. Please check permissions.');
      }
      console.error('Audio device enumeration error:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    getAudioDevices();
    navigator.mediaDevices.addEventListener('devicechange', getAudioDevices);
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', getAudioDevices);
    };
  }, [getAudioDevices]);

  useEffect(() => {
    if (audioDevices.length > 0) {
      const isValidSelection = audioDevices.some(d => d.deviceId === selectedDeviceId);
      if (!selectedDeviceId || !isValidSelection) {
        const newDeviceId = audioDevices[0].deviceId;
        setSelectedDeviceId(newDeviceId);
      }
    }
  }, [audioDevices, selectedDeviceId]);

  useEffect(() => {
    if (selectedDeviceId) {
      localStorage.setItem('selectedAudioDevice', selectedDeviceId);
    }
  }, [selectedDeviceId]);

  const selectDevice = (deviceId: string) => {
    setSelectedDeviceId(deviceId);
  };

  const audioConstraints = useMemo(() => {
    if (!selectedDeviceId) {
      return true;
    }

    // Use 'ideal' instead of 'exact' to be more flexible
    // This allows fallback to other devices if the selected one fails
    return {
      deviceId: { ideal: selectedDeviceId },
      // Add some basic audio constraints for better compatibility
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    };
  }, [selectedDeviceId]);

  // Provide fallback constraints for when the selected device fails
  const getFallbackConstraints = useCallback(() => {
    return {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    };
  }, []);

  return {
    audioDevices,
    selectedDeviceId,
    selectDevice,
    isLoading,
    error,
    audioConstraints,
    getFallbackConstraints,
  };
};
