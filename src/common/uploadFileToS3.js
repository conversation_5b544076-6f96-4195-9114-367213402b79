import fs from "fs";
import logger from "./utils/logger";
import AWS from "aws-sdk";

const S3 = new AWS.S3();

export default async function uploadFileToS3({
  targetFileKey,
  file,
  filePath,
  targetBucket,
}) {
  logger.info(`Start of s3 upload process for ${filePath}`);
  try {
    const outputData = file || (await fs.promises.readFile(filePath));

    logger.info("File read into outputData for S3 putParams");

    const putParams = {
      ACL: "public-read",
      Bucket: targetBucket,
      Key: targetFileKey,
      Body: outputData,
    };

    const result = await S3.putObject(putParams).promise();

    logger.info(`Successfully uploaded file ${result.Key} to S3`);
  } catch (e) {
    logger.error(JSON.stringify(e));
  }
}
