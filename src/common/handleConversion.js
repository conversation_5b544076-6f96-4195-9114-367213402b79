import fs from "fs";
import path from "path";
import logger from "./utils/logger";
import parseEvent from "./parseEvent";
import formats from "./formats";
import { convertToMp4 } from "../conversionHandlers/mp4/convertToMp4";
import convertToOgv from "../conversionHandlers/ogv/convertToOgv";
import convertToWebm from "../conversionHandlers/webm/convertToWebm";
import updateVideoStatus from "../conversionHandlers/updateVideoStatus";
import downLoadFileFromS3 from "./downLoadFileFromS3";
import uploadFileToS3 from "./uploadFileToS3";

function getInputAndOutputPath({ format, sourceKey }) {
  const inputPath = `/tmp/${path.basename(sourceKey)}`;
  const outputPath = `/tmp/output.${format}`;
  return { inputPath, outputPath };
}
async function saveFileToTemp({ inputData, inputPath }) {
  await fs.promises.writeFile(inputPath, inputData.Body);
  logger.info(`Saved downloaded file from S3 to ${inputPath}`);
}
async function convert({
  format,
  inputPath,
  outputPath,
  sourceKey,
  inputData,
  targetBucket,
  duration,
}) {
  if (format === formats.mp4) {
    await convertToMp4({
      inputPath,
      outputPath,
      sourceKey,
      inputData,
      targetBucket,
      duration,
    });
  }

  if (format === formats.ogv) {
    await convertToOgv({ inputPath, outputPath, sourceKey });
  }

  if (format === formats.webm) {
    await convertToWebm({
      inputPath,
      outputPath,
      sourceKey,
      duration,
      targetBucket,
      inputData,
    });
  }

  logger.info(`Successfully converted file to ${format}`);
}

async function uploadToS3({ format, outputPath, targetBucket, sourceKey }) {
  const targetFileKey = 'video_parts/' + sourceKey.replace('original', 'converted').replace(
    path.extname(sourceKey),
    `.${format}`
  );

  logger.info('Uploading...', targetFileKey);

  await uploadFileToS3({
    targetBucket,
    targetFileKey,
    filePath: outputPath,
  });
}

async function handleConversion({ event, format }) {
  logger.debug("Request received:", JSON.stringify(event));
  const { sourceBucket, sourceKey, targetBucket, duration } = parseEvent(event);

  if (!sourceKey) {
    logger.error("No sourceKey present in body");

    return {
      statusCode: 400,
      body: JSON.stringify("No sourceKey present in body"),
    };
  }
  try {
    const inputData = await downLoadFileFromS3({ sourceBucket, sourceKey });

    const { inputPath, outputPath } = getInputAndOutputPath({
      format,
      sourceKey,
    });

    await saveFileToTemp({ inputData, inputPath });

    await convert({
      format,
      inputPath,
      outputPath,
      sourceKey,
      inputData,
      targetBucket,
      duration,
    });

    await uploadToS3({ format, outputPath, sourceKey, targetBucket });

    await updateVideoStatus({ format, status: "done", videoKey: sourceKey });

    return {
      statusCode: 200,
      body: JSON.stringify(
        `Successfully processed and uploaded the ${format} video to ${targetBucket}`
      ),
    };
  } catch (error) {
    logger.error("Error processing file: ", error);

    await updateVideoStatus({ format, status: "error", videoKey: sourceKey });

    return {
      statusCode: 500,
      body: JSON.stringify(`Error processing file: ${error}`),
    };
  }
}

export default handleConversion;
