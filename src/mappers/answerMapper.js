import globals from './_globals.js';
import questionMapper from './questionMapper.js';
import voteMapper from './voteMapper.js';
import likeMapper from './likeMapper.js';
import commentMapper from './commentMapper.js';
import questionShareMapper from './questionShareMapper.js';

function toFullDTO ( entity ) {
	const globalAttributes = globals.toDTOSuper( entity );
	const modelAttributes = {
		clientId: entity.clientId,
		imageUrl: entity.imageUrl,
		mp4VideoStatus: entity.mp4VideoStatus,
		ogvVideoStatus: entity.ogvVideoStatus,
		webmVideoStatus: entity.webmVideoStatus,
		videoUrl: entity.videoUrl,
		videoUrls: entity.videoUrls,
		videoDuration: entity.videoDuration,
		subtitles: entity.subtitles,
		subtitlesSpeed: entity.subtitlesSpeed,
		subtitlesTranslations: entity.subtitlesTranslations,
		transcription: entity.transcription,
		transcriptionTranslation: entity.transcriptionTranslation,
		showTranscribedSubtitles: entity.showTranscribedSubtitles,
		votes: 1,
		question: { id: entity.questionId },

		createdAtDateString: entity.createdAt.toDateString().match( /[a-zA-Z]{3,4} \d{1,2}/ )[0],

		comments: [],
		commented: false,
		userVotes: voteMapper.toListDTO( entity.votes ),
		userLikes: likeMapper.toListDTO( entity.likes ),
		likes: [],
		liked: false,

		isDraft:    entity.isDraft,
		isApproved: entity.isApproved,
		isDenied:   entity.isDenied,
		isShared:   entity.isShared,
		isPinned:   entity.isPinned,

		endDate: entity.endDate,

		uploadId: entity.uploadId,
		videoKey: entity.videoKey,
		convertedChunkIndexes: entity.convertedChunkIndexes,
		failedChunkConversions: entity.failedChunkConversions
	};

	var result = Object.assign( globalAttributes, modelAttributes );

	if ( entity.question ) {
		result.question = questionMapper.toFullDTO( entity.question );
		result.votes += voteMapper.toAmount( entity.question.votes );
	}

	if ( entity.comments )
		result.comments = commentMapper.toListDTO( entity.comments );

	if ( entity.likes ) {
		result.userLikes = entity.likes;
		result.likes = likeMapper.toAmount( entity.likes );
	}

	if (entity.question_shares)
		result.questionShares = questionShareMapper.toListDTO( entity.question_shares );

	if (entity.isShared)
		result.sentBy = (entity.user || {}).email || null;

	return result;
}

function toListDTO ( entities ) {
	return entities.map( toFullDTO );
}

function toPartnerApiDTO ( entity ) {
	// Construct embed URL using client name and answer ID
	const clientName = entity.client ? entity.client.name : 'unknown';
	const embedUrl = `https://embed.repd.us/${clientName}?answerId=${entity.id}`;

	return {
		id: entity.id,
		transcription: entity.transcription,
		url: embedUrl,
		question: entity.question.text
	};
}

function toPartnerApiListDTO ( entities ) {
	return entities.map( toPartnerApiDTO );
}

export default {
	toFullDTO,
	toListDTO,
	toPartnerApiDTO,
	toPartnerApiListDTO
};
