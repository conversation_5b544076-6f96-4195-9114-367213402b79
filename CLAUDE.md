# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Terraform infrastructure-as-code repository for the Repd application, managing AWS resources across staging and production environments. The infrastructure supports a serverless architecture with Lambda functions, API Gateway, and various supporting services.

## Architecture

### Core Infrastructure Components

- **Lambda Functions**: Video processing pipeline with multiple conversion formats (MP4, OGV, WebM)
- **API Gateway**: REST API with endpoints for AI and data services (`/ai-service`, `/data-service`)
- **Specialized Services**: 
  - AI Service: Integrates with OpenAI and Anthropic APIs for AI functionality
  - Data Service: Database operations and data management
  - Video Status Updater: Tracks video processing status
  - Email Sender: SendGrid integration for email notifications

### Environment Structure

The infrastructure is deployed to two environments:
- **Staging**: `staging` environment for development and testing
- **Production**: `prod` environment for live application

Both environments use identical infrastructure patterns but separate resources and configurations.

### External Dependencies

- **Database**: PostgreSQL database (hosted externally)
- **Supabase**: Authentication and additional database services
- **S3**: File storage in `repd-api-files` bucket
- **SendGrid**: Email delivery service
- **FFmpeg**: Video processing via AWS Lambda layer

## Common Terraform Commands

### Planning and Deployment
```bash
# Initialize Terraform (run first time or after module changes)
terraform init

# Plan changes for review
terraform plan

# Apply changes to infrastructure
terraform apply

# Destroy infrastructure (use with caution)
terraform destroy
```

### Environment-Specific Operations
```bash
# Target specific modules
terraform plan -target=module.lambda_functions--staging
terraform apply -target=module.lambda_functions--prod

# Refresh state
terraform refresh
```

### Validation and Formatting
```bash
# Validate configuration
terraform validate

# Format code
terraform fmt -recursive
```

## Key Configuration Details

### Lambda Configuration
- **Runtime**: Node.js 20.x
- **Memory**: 10,240 MB (10GB)
- **Timeout**: 900 seconds (15 minutes)
- **Ephemeral Storage**: 10,240 MB
- **VPC**: Configured with specific subnet and security groups

### Required Environment Variables
All sensitive variables are defined in `variables.tf` and must be provided via terraform.tfvars or environment variables:
- Database credentials (`DB_HOST_STAGING`, `DB_HOST_PROD`, `DB_USER`, `DB_PASSWORD`)
- API keys (`ANTHROPIC_API_KEY`, `OPEN_AI_DEV_KEY`, `SEND_GRID_KEY`)
- Supabase configuration (`SUPABASE_URL`, `SUPABASE_PUBLIC_KEY`, `SUPABASE_SERVICE_ROLE`)

### Module Structure
```
modules/
├── api_gateway/          # REST API configuration
├── ec2_instances/        # EC2 spot instances (currently commented out)
├── lambda_functions/     # Core Lambda functions
├── lambdas/
│   ├── ai_service/       # AI processing service
│   └── data_service/     # Data management service
└── policy_and_role/      # IAM roles and policies
```

## Development Workflow

1. **Making Changes**: Modify Terraform files in appropriate module directories
2. **Testing**: Always run `terraform plan` to review changes before applying
3. **Deployment**: Use `terraform apply` to deploy changes
4. **Lambda Updates**: Lambda code deployments require updating the `function.zip` file in the lambda_functions module

## Important Notes

- All Lambda functions share a single deployment package (`function.zip`)
- FFmpeg layer is used for video conversion functions
- API Gateway automatically triggers redeployment when integration changes are detected
- IAM roles provide access to S3, CloudWatch Logs, VPC, and Lambda invocation
- Spot instances for video processing are currently disabled but available in configuration