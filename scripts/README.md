# Database Maintenance Scripts

This directory contains scripts for maintaining and cleaning up the database.

## Marvin Database Video Pruning Script

### Overview

The `prune-marvin-videos.js` script is designed to clean up the Marvin database by removing orphaned video-related records that no longer have corresponding entries in the main database.

### What it does

The script identifies and optionally removes:

1. **Orphaned Answer Embeddings**: Records in the `answer_embeddings` table that reference:
   - Non-existent answer IDs in the main database
   - Non-existent client IDs in the main database

2. **Orphaned Question Embeddings**: Records in the `question_embeddings` table that reference:
   - Non-existent client IDs in the main database

### Usage

#### Dry Run (Recommended First)
```bash
# Show what would be deleted without making any changes
node scripts/prune-marvin-videos.js --dry-run

# Or simply run without arguments (defaults to dry-run)
node scripts/prune-marvin-videos.js
```

#### Execute Deletion
```bash
# Actually delete the orphaned records
node scripts/prune-marvin-videos.js --execute
```

### Output

The script provides detailed output including:

- 🔍 **Discovery Phase**: Shows how many records are being checked
- 📊 **Analysis**: Lists orphaned records with reasons why they're orphaned
- 📈 **Summary Report**: Shows database statistics before and after cleanup
- ✅ **Execution**: Shows progress of deletion operations (if executed)

### Example Output

```
🚀 Starting Marvin DB video pruning script...
Mode: DRY RUN (no changes will be made)
✅ Database connections established

🔍 Checking for orphaned answer embeddings...
📊 Found 1,234 answer embeddings in Marvin DB
📊 Found 1,200 valid answers in main DB
📊 Found 45 valid clients in main DB
❌ Found 34 orphaned answer embeddings

Orphaned answer embeddings:
  - ID: 567, Supabase ID: 12345, Reason: invalid answer_id: 999
    Question: "What are the city's plans for downtown development..."

🔍 Checking for orphaned question embeddings...
📊 Found 2,456 question embeddings in Marvin DB
📊 Found 45 valid clients in main DB
❌ Found 12 orphaned question embeddings

📊 Summary:
  - Orphaned answer embeddings: 34
  - Orphaned question embeddings: 12
  - Total orphaned records: 46

📈 Database Summary:
  Main Database:
    - Active answers: 1,200
    - Active clients: 45
    - Questions: 3,456
  Marvin Database:
    - Answer embeddings: 1,234
    - Question embeddings: 2,456
    - Answer embeddings with REPD ID: 1,200
    - Question embeddings with client ID: 2,400

💡 Run with --execute to delete these orphaned records
```

### Safety Features

1. **Dry Run Default**: The script defaults to dry-run mode to prevent accidental deletions
2. **Batch Processing**: Large deletions are processed in batches of 100 records to avoid query size limits
3. **Detailed Logging**: Every operation is logged with clear status indicators
4. **Connection Management**: Database connections are properly closed even if errors occur
5. **Error Handling**: Comprehensive error handling with descriptive messages

### Prerequisites

- Node.js environment with access to the database connections
- Proper database credentials configured in the environment
- Access to both main database and Marvin database

### Database Schema Dependencies

The script expects the following tables to exist:

**Main Database:**
- `answers` table with columns: `id`, `client_id`, `enabled`
- `clients` table with columns: `id`, `enabled`

**Marvin Database:**
- `answer_embeddings` table with columns: `id`, `supabase_id`, `repd_answer_id`, `repd_client_id`, `question`, `transcription`, `created_at`
- `question_embeddings` table with columns: `id`, `client_id`, `user_id`, `question`, `answer`, `created_at`

### When to Run

Consider running this script:

- After bulk deletions of answers or clients in the main database
- As part of regular database maintenance (monthly/quarterly)
- When you notice performance issues with the Marvin database
- Before major system updates or migrations

### Monitoring

The script provides comprehensive statistics that can help you monitor:

- Database growth patterns
- Orphaned record accumulation
- Cleanup effectiveness

### Troubleshooting

**Connection Issues:**
- Verify database credentials and network connectivity
- Check that both main and Marvin databases are accessible

**Permission Issues:**
- Ensure the database user has SELECT permissions on main database tables
- Ensure the database user has SELECT and DELETE permissions on Marvin database tables

**Large Dataset Performance:**
- The script processes deletions in batches to handle large datasets
- For extremely large datasets, consider running during off-peak hours

### Related Scripts

This script is part of a larger database maintenance toolkit. Other related scripts may include:

- Video file cleanup scripts
- Embedding synchronization scripts
- Database optimization scripts

### Contributing

When modifying this script:

1. Test thoroughly with `--dry-run` first
2. Add appropriate logging for new operations
3. Maintain batch processing for large operations
4. Update this documentation for any new features
