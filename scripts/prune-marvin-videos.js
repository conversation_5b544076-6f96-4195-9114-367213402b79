#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to prune the <PERSON> database of videos that do not exist or match videos in the main database
 * 
 * This script will:
 * 1. Find answer_embeddings in Marvin DB that reference non-existent answers in main DB
 * 2. Find question_embeddings in Marvin DB that reference non-existent clients in main DB
 * 3. Optionally delete orphaned records (with confirmation)
 * 
 * Usage:
 *   node scripts/prune-marvin-videos.js --dry-run    # Show what would be deleted
 *   node scripts/prune-marvin-videos.js --execute    # Actually delete orphaned records
 */

import { db, dbMarvin } from '../src/global/database.js';
import logger from '../src/global/log.js';
import { Op } from 'sequelize';

// Command line argument parsing
const args = process.argv.slice(2);
const isDryRun = args.includes('--dry-run') || args.length === 0;
const shouldExecute = args.includes('--execute');

if (!isDryRun && !shouldExecute) {
	console.log('Usage:');
	console.log('  node scripts/prune-marvin-videos.js --dry-run    # Show what would be deleted');
	console.log('  node scripts/prune-marvin-videos.js --execute    # Actually delete orphaned records');
	process.exit(1);
}

/**
 * Find orphaned answer embeddings in Marvin DB
 */
async function findOrphanedAnswerEmbeddings() {
	console.log('\n🔍 Checking for orphaned answer embeddings...');
    
	try {
		// Get all answer embeddings from Marvin DB with their referenced answer IDs
		const [marvinAnswerEmbeddings] = await dbMarvin.query(`
            SELECT 
                id,
                supabase_id,
                repd_answer_id,
                repd_client_id,
                question,
                transcription,
                created_at
            FROM answer_embeddings 
            WHERE repd_answer_id IS NOT NULL
            ORDER BY id
        `);

		if (marvinAnswerEmbeddings.length === 0) {
			console.log('✅ No answer embeddings found in Marvin DB');
			return [];
		}

		console.log(`📊 Found ${marvinAnswerEmbeddings.length} answer embeddings in Marvin DB`);

		// Get all valid answer IDs from main DB
		const [validAnswers] = await db.query(`
            SELECT DISTINCT id, client_id, enabled
            FROM answers 
            WHERE enabled = true
        `);

		const validAnswerIds = new Set(validAnswers.map(a => a.id));
		const validClientIds = new Set(validAnswers.map(a => a.client_id));

		console.log(`📊 Found ${validAnswerIds.size} valid answers in main DB`);
		console.log(`📊 Found ${validClientIds.size} valid clients in main DB`);

		// Find orphaned embeddings
		const orphanedEmbeddings = marvinAnswerEmbeddings.filter(embedding => {
			const hasInvalidAnswer = embedding.repd_answer_id && !validAnswerIds.has(embedding.repd_answer_id);
			const hasInvalidClient = embedding.repd_client_id && !validClientIds.has(embedding.repd_client_id);
			return hasInvalidAnswer || hasInvalidClient;
		});

		console.log(`❌ Found ${orphanedEmbeddings.length} orphaned answer embeddings`);

		if (orphanedEmbeddings.length > 0) {
			console.log('\nOrphaned answer embeddings:');
			orphanedEmbeddings.forEach(embedding => {
				const reason = [];
				if (embedding.repd_answer_id && !validAnswerIds.has(embedding.repd_answer_id)) {
					reason.push(`invalid answer_id: ${embedding.repd_answer_id}`);
				}
				if (embedding.repd_client_id && !validClientIds.has(embedding.repd_client_id)) {
					reason.push(`invalid client_id: ${embedding.repd_client_id}`);
				}
                
				console.log(`  - ID: ${embedding.id}, Supabase ID: ${embedding.supabase_id}, Reason: ${reason.join(', ')}`);
				if (embedding.question) {
					console.log(`    Question: "${embedding.question.substring(0, 100)}${embedding.question.length > 100 ? '...' : ''}"`);
				}
			});
		}

		return orphanedEmbeddings;
	} catch (error) {
		console.error('❌ Error finding orphaned answer embeddings:', error);
		throw error;
	}
}

/**
 * Find orphaned question embeddings in Marvin DB
 */
async function findOrphanedQuestionEmbeddings() {
	console.log('\n🔍 Checking for orphaned question embeddings...');
    
	try {
		// Get all question embeddings from Marvin DB
		const [marvinQuestionEmbeddings] = await dbMarvin.query(`
            SELECT 
                id,
                client_id,
                user_id,
                question,
                answer,
                created_at
            FROM question_embeddings 
            WHERE client_id IS NOT NULL
            ORDER BY id
        `);

		if (marvinQuestionEmbeddings.length === 0) {
			console.log('✅ No question embeddings found in Marvin DB');
			return [];
		}

		console.log(`📊 Found ${marvinQuestionEmbeddings.length} question embeddings in Marvin DB`);

		// Get all valid client IDs from main DB
		const [validClients] = await db.query(`
            SELECT DISTINCT id
            FROM clients 
            WHERE enabled = true
        `);

		const validClientIds = new Set(validClients.map(c => c.id));
		console.log(`📊 Found ${validClientIds.size} valid clients in main DB`);

		// Find orphaned question embeddings
		const orphanedQuestionEmbeddings = marvinQuestionEmbeddings.filter(embedding => {
			return embedding.client_id && !validClientIds.has(embedding.client_id);
		});

		console.log(`❌ Found ${orphanedQuestionEmbeddings.length} orphaned question embeddings`);

		if (orphanedQuestionEmbeddings.length > 0) {
			console.log('\nOrphaned question embeddings:');
			orphanedQuestionEmbeddings.forEach(embedding => {
				console.log(`  - ID: ${embedding.id}, Client ID: ${embedding.client_id}, User ID: ${embedding.user_id}`);
				if (embedding.question) {
					console.log(`    Question: "${embedding.question.substring(0, 100)}${embedding.question.length > 100 ? '...' : ''}"`);
				}
			});
		}

		return orphanedQuestionEmbeddings;
	} catch (error) {
		console.error('❌ Error finding orphaned question embeddings:', error);
		throw error;
	}
}

/**
 * Delete orphaned records from Marvin DB
 */
async function deleteOrphanedRecords(orphanedAnswerEmbeddings, orphanedQuestionEmbeddings) {
	console.log('\n🗑️  Deleting orphaned records...');

	let deletedCount = 0;

	try {
		// Delete orphaned answer embeddings in batches to avoid query size limits
		if (orphanedAnswerEmbeddings.length > 0) {
			const batchSize = 100;
			for (let i = 0; i < orphanedAnswerEmbeddings.length; i += batchSize) {
				const batch = orphanedAnswerEmbeddings.slice(i, i + batchSize);
				const answerEmbeddingIds = batch.map(e => e.id);

				await dbMarvin.query(`
                    DELETE FROM answer_embeddings
                    WHERE id IN (${answerEmbeddingIds.join(',')})
                `);

				console.log(`✅ Deleted batch of ${batch.length} answer embeddings (${i + batch.length}/${orphanedAnswerEmbeddings.length})`);
			}
			deletedCount += orphanedAnswerEmbeddings.length;
		}

		// Delete orphaned question embeddings in batches
		if (orphanedQuestionEmbeddings.length > 0) {
			const batchSize = 100;
			for (let i = 0; i < orphanedQuestionEmbeddings.length; i += batchSize) {
				const batch = orphanedQuestionEmbeddings.slice(i, i + batchSize);
				const questionEmbeddingIds = batch.map(e => e.id);

				await dbMarvin.query(`
                    DELETE FROM question_embeddings
                    WHERE id IN (${questionEmbeddingIds.join(',')})
                `);

				console.log(`✅ Deleted batch of ${batch.length} question embeddings (${i + batch.length}/${orphanedQuestionEmbeddings.length})`);
			}
			deletedCount += orphanedQuestionEmbeddings.length;
		}

		console.log(`\n🎉 Total records deleted: ${deletedCount}`);

	} catch (error) {
		console.error('❌ Error deleting orphaned records:', error);
		throw error;
	}
}

/**
 * Generate a summary report
 */
async function generateSummaryReport() {
	console.log('\n📊 Generating database summary report...');

	try {
		// Get counts from Marvin DB
		const [marvinCounts] = await dbMarvin.query(`
            SELECT
                (SELECT COUNT(*) FROM answer_embeddings) as answer_embeddings_count,
                (SELECT COUNT(*) FROM question_embeddings) as question_embeddings_count,
                (SELECT COUNT(*) FROM answer_embeddings WHERE repd_answer_id IS NOT NULL) as answer_embeddings_with_repd_id,
                (SELECT COUNT(*) FROM question_embeddings WHERE client_id IS NOT NULL) as question_embeddings_with_client_id
        `);

		// Get counts from main DB
		const [mainCounts] = await db.query(`
            SELECT
                (SELECT COUNT(*) FROM answers WHERE enabled = true) as active_answers_count,
                (SELECT COUNT(*) FROM clients WHERE enabled = true) as active_clients_count,
                (SELECT COUNT(*) FROM questions) as questions_count
        `);

		console.log('\n📈 Database Summary:');
		console.log('  Main Database:');
		console.log(`    - Active answers: ${mainCounts[0].active_answers_count}`);
		console.log(`    - Active clients: ${mainCounts[0].active_clients_count}`);
		console.log(`    - Questions: ${mainCounts[0].questions_count}`);
		console.log('  Marvin Database:');
		console.log(`    - Answer embeddings: ${marvinCounts[0].answer_embeddings_count}`);
		console.log(`    - Question embeddings: ${marvinCounts[0].question_embeddings_count}`);
		console.log(`    - Answer embeddings with REPD ID: ${marvinCounts[0].answer_embeddings_with_repd_id}`);
		console.log(`    - Question embeddings with client ID: ${marvinCounts[0].question_embeddings_with_client_id}`);

	} catch (error) {
		console.error('❌ Error generating summary report:', error);
	}
}

/**
 * Main execution function
 */
async function main() {
	console.log('🚀 Starting Marvin DB video pruning script...');
	console.log(`Mode: ${isDryRun ? 'DRY RUN (no changes will be made)' : 'EXECUTE (will delete orphaned records)'}`);
    
	try {
		// Test database connections
		await db.authenticate();
		await dbMarvin.authenticate();
		console.log('✅ Database connections established');

		// Find orphaned records
		const orphanedAnswerEmbeddings = await findOrphanedAnswerEmbeddings();
		const orphanedQuestionEmbeddings = await findOrphanedQuestionEmbeddings();

		const totalOrphaned = orphanedAnswerEmbeddings.length + orphanedQuestionEmbeddings.length;

		if (totalOrphaned === 0) {
			console.log('\n🎉 No orphaned records found! Marvin DB is clean.');
			return;
		}

		console.log('\n📊 Summary:');
		console.log(`  - Orphaned answer embeddings: ${orphanedAnswerEmbeddings.length}`);
		console.log(`  - Orphaned question embeddings: ${orphanedQuestionEmbeddings.length}`);
		console.log(`  - Total orphaned records: ${totalOrphaned}`);

		// Generate summary report
		await generateSummaryReport();

		if (shouldExecute) {
			console.log('\n⚠️  WARNING: This will permanently delete orphaned records!');

			// In a real script, you might want to add a confirmation prompt here
			// For now, we'll proceed with deletion
			await deleteOrphanedRecords(orphanedAnswerEmbeddings, orphanedQuestionEmbeddings);

			// Generate post-deletion report
			console.log('\n📊 Post-deletion summary:');
			await generateSummaryReport();
		} else {
			console.log('\n💡 Run with --execute to delete these orphaned records');
		}

	} catch (error) {
		console.error('❌ Script failed:', error);
		process.exit(1);
	} finally {
		// Close database connections
		await db.close();
		await dbMarvin.close();
		console.log('✅ Database connections closed');
	}
}

// Run the script
main().catch(error => {
	console.error('❌ Unhandled error:', error);
	process.exit(1);
});
