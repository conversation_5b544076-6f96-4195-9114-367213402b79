// Script to remove emails from SendGrid global unsubscribe list based on configurable filters
import { SendGrid } from '../../src/global/mail.js';

// Define filters - easy to add new ones here
const filters = [
	{
		name: 'Suisun domain',
		match: (unsubscribe) => unsubscribe.email && unsubscribe.email.toLowerCase().includes('@suisun')
	},
	{
		name: 'Government domain (.gov)',
		match: (unsubscribe) => unsubscribe.email && /.gov$/i.test(unsubscribe.email)
	},
	{
		name: 'Richland domain',
		match: (unsubscribe) => unsubscribe.email && unsubscribe.email.toLowerCase().includes('@richland.wa.us')
	},
	{
		name: 'City of Lorain',
		match: (unsubscribe) => unsubscribe.email && unsubscribe.email.toLowerCase().includes('cityoflorain.org')
	},
	// Add more filters here as needed:
	// {
	//   name: 'Another filter',
	//   match: (unsubscribe) => unsubscribe.someProperty && unsubscribe.someProperty.includes('some text')
	// },
];

async function processUnsubscribes() {
	try {
		console.log('Starting to process unsubscribes...');
    
		let processedCount = 0;
		let matchCounts = {};
		filters.forEach(filter => { matchCounts[filter.name] = 0; });
    
		let offset = 0;
		const limit = 100; // Process in smaller batches
		let hasMoreRecords = true;
    
		// Process in batches until we've gone through all records
		while (hasMoreRecords) {
			// Get unsubscribed emails from SendGrid with pagination
			const request = {
				method: 'GET',
				url: '/v3/suppression/unsubscribes',
				qs: {
					limit: limit,
					offset: offset
				}
			};
      
			console.log(`Fetching batch with offset ${offset}...`);
			let [response, body] = await SendGrid.request(request);
      
			if (!Array.isArray(body)) {
				console.error('Unexpected response format:', body);
				return;
			}
      
			// If we got fewer records than requested, we've reached the end
			if (body.length < limit) {
				hasMoreRecords = false;
			}
      
			processedCount += body.length;
			console.log(`Processing ${body.length} unsubscribed emails in this batch`);
      
			// Apply all filters and collect matches
			let allUnsubscribesToRemove = new Set();
      
			// Process each filter
			for (const filter of filters) {
				const matches = body.filter(filter.match);
				matchCounts[filter.name] += matches.length;
				console.log(`Found ${matches.length} matches for "${filter.name}" in this batch`);
        
				// Add to our set of unsubscribes to remove
				matches.forEach(match => allUnsubscribesToRemove.add(match));
			}
      
			// Convert back to array
			const unsubscribesToRemove = Array.from(allUnsubscribesToRemove);
      
			// Process each unsubscribed email
			for (const unsubscribe of unsubscribesToRemove) {
				try {
					// Delete from unsubscribe list
					const deleteRequest = {
						method: 'DELETE',
						url: '/v3/suppression/unsubscribes',
						body: {
							emails: [unsubscribe.email]
						}
					};
          
					await SendGrid.request(deleteRequest);
					console.log(`✅ Successfully removed ${unsubscribe.email} from unsubscribe list`);
				} catch (error) {
					console.error(`❌ Failed to remove ${unsubscribe.email} from unsubscribe list:`, error.message);
				}
			}
      
			// Increment offset for next batch
			offset += limit;
      
			// Add a small delay to avoid rate limiting
			await new Promise(resolve => setTimeout(resolve, 1000));
		}
    
		console.log('Completed processing unsubscribed emails.');
		console.log(`Processed ${processedCount} total records.`);
    
		// Report matches for each filter
		for (const [filterName, count] of Object.entries(matchCounts)) {
			console.log(`Found ${count} matches for "${filterName}"`);
		}
	} catch (error) {
		console.error('Error running script:', error);
	}
}

// Export the function for use in scheduled tasks
export { processUnsubscribes };

// Execute the function only if this script is run directly
// if (import.meta.url === `file://${process.argv[1]}`) {
processUnsubscribes();
// }
