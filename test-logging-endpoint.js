#!/usr/bin/env node

/**
 * Simple test script to verify the logging endpoint works
 * Run with: node test-logging-endpoint.js
 */

import axios from 'axios';

const API_BASE_URL = process.env.API_URL || 'http://localhost:3000';
const API_ENDPOINT = `${API_BASE_URL}/api/v1.0.0/logs`;
const BATCH_ENDPOINT = `${API_BASE_URL}/api/v1.0.0/logs/batch`;

// Test authentication token (you'll need to replace this with a valid token)
const AUTH_TOKEN = process.env.AUTH_TOKEN || 'your-auth-token-here';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': AUTH_TOKEN
};

async function testSingleLog() {
  console.log('Testing single log endpoint...');
  
  try {
    const response = await axios.post(API_ENDPOINT, {
      level: 'info',
      message: 'Test log message from script',
      metadata: {
        testData: 'This is a test',
        timestamp: new Date().toISOString()
      },
      source: 'test-script',
      action: 'test_action',
      category: 'testing'
    }, { headers });

    console.log('✅ Single log test passed');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Single log test failed');
    console.log('Error:', error.response?.data || error.message);
    return false;
  }
}

async function testBatchLogs() {
  console.log('\nTesting batch logs endpoint...');
  
  try {
    const response = await axios.post(BATCH_ENDPOINT, {
      logs: [
        {
          level: 'info',
          message: 'First batch log message',
          action: 'batch_test_1',
          category: 'testing'
        },
        {
          level: 'debug',
          message: 'Second batch log message',
          metadata: { batchIndex: 2 },
          action: 'batch_test_2',
          category: 'testing'
        },
        {
          level: 'warn',
          message: 'Third batch log message with warning',
          action: 'batch_test_3',
          category: 'testing'
        }
      ]
    }, { headers });

    console.log('✅ Batch logs test passed');
    console.log('Response:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Batch logs test failed');
    console.log('Error:', error.response?.data || error.message);
    return false;
  }
}

async function testInvalidData() {
  console.log('\nTesting validation with invalid data...');
  
  try {
    const response = await axios.post(API_ENDPOINT, {
      level: 'invalid-level', // This should fail validation
      message: 'Test message'
    }, { headers });

    console.log('❌ Validation test failed - should have rejected invalid level');
    return false;
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Validation test passed - correctly rejected invalid data');
      console.log('Validation error:', error.response.data);
      return true;
    } else {
      console.log('❌ Validation test failed - unexpected error');
      console.log('Error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function testHealthCheck() {
  console.log('Testing server health check...');
  
  try {
    const response = await axios.get(`${API_BASE_URL}/health_check`);
    console.log('✅ Server is running');
    console.log('Health check response:', response.data);
    return true;
  } catch (error) {
    console.log('❌ Server health check failed');
    console.log('Error:', error.message);
    console.log('Make sure the server is running on', API_BASE_URL);
    return false;
  }
}

async function runTests() {
  console.log('🧪 Starting logging endpoint tests...');
  console.log('API Base URL:', API_BASE_URL);
  console.log('Auth Token:', AUTH_TOKEN ? 'Provided' : 'Not provided (tests may fail)');
  console.log('');

  const results = [];

  // Test server health first
  results.push(await testHealthCheck());

  if (results[0]) {
    // Only run other tests if server is healthy
    results.push(await testSingleLog());
    results.push(await testBatchLogs());
    results.push(await testInvalidData());
  }

  console.log('\n📊 Test Results:');
  console.log('Health Check:', results[0] ? '✅' : '❌');
  if (results.length > 1) {
    console.log('Single Log:', results[1] ? '✅' : '❌');
    console.log('Batch Logs:', results[2] ? '✅' : '❌');
    console.log('Validation:', results[3] ? '✅' : '❌');
  }

  const passedTests = results.filter(Boolean).length;
  const totalTests = results.length;

  console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed`);

  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! The logging endpoint is working correctly.');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Check the errors above.');
    process.exit(1);
  }
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: node test-logging-endpoint.js [options]

Options:
  --help, -h     Show this help message

Environment Variables:
  API_URL        Base URL for the API (default: http://localhost:3000)
  AUTH_TOKEN     Authentication token for API requests

Examples:
  node test-logging-endpoint.js
  API_URL=http://localhost:3000 AUTH_TOKEN=your-token node test-logging-endpoint.js
  `);
  process.exit(0);
}

// Run the tests
runTests().catch(error => {
  console.error('Unexpected error running tests:', error);
  process.exit(1);
});
