<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Constraints Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        #deviceList {
            margin: 10px 0;
        }
        select {
            padding: 5px;
            margin: 5px;
        }
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background-color: #000;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Audio Constraints Test</h1>
    <p>This test helps diagnose audio constraint issues in video recording.</p>

    <div class="test-section">
        <h2>Step 1: Enumerate Audio Devices</h2>
        <button onclick="enumerateDevices()">Get Audio Devices</button>
        <div id="deviceList"></div>
    </div>

    <div class="test-section">
        <h2>Step 2: Test Audio Constraints</h2>
        <select id="deviceSelect">
            <option value="">Select a device...</option>
        </select>
        <br>
        <button onclick="testExactConstraint()">Test Exact Constraint</button>
        <button onclick="testIdealConstraint()">Test Ideal Constraint</button>
        <button onclick="testBasicConstraint()">Test Basic Constraint</button>
        <button onclick="stopStream()">Stop Stream</button>
    </div>

    <div class="test-section">
        <h2>Step 3: Video Preview</h2>
        <video id="videoPreview" autoplay muted playsinline></video>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="log" class="log"></div>
    </div>

    <script>
        let currentStream = null;
        let audioDevices = [];

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            if (type === 'error') {
                console.error(message);
            } else {
                console.log(message);
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        async function enumerateDevices() {
            try {
                log('Requesting permission to enumerate devices...');
                
                // First get permission
                const permissionStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                permissionStream.getTracks().forEach(track => track.stop());
                
                log('Permission granted, enumerating devices...');
                const devices = await navigator.mediaDevices.enumerateDevices();
                audioDevices = devices.filter(device => device.kind === 'audioinput');
                
                log(`Found ${audioDevices.length} audio input devices`);
                
                const deviceList = document.getElementById('deviceList');
                const deviceSelect = document.getElementById('deviceSelect');
                
                deviceList.innerHTML = '';
                deviceSelect.innerHTML = '<option value="">Select a device...</option>';
                
                audioDevices.forEach((device, index) => {
                    const label = device.label || `Microphone ${device.deviceId.slice(-4)}`;
                    log(`Device ${index + 1}: ${label} (${device.deviceId})`);
                    
                    const div = document.createElement('div');
                    div.textContent = `${index + 1}. ${label}`;
                    deviceList.appendChild(div);
                    
                    const option = document.createElement('option');
                    option.value = device.deviceId;
                    option.textContent = label;
                    deviceSelect.appendChild(option);
                });
                
                if (audioDevices.length > 0) {
                    deviceSelect.value = audioDevices[0].deviceId;
                }
                
            } catch (error) {
                log(`Error enumerating devices: ${error.name} - ${error.message}`, 'error');
            }
        }

        async function testExactConstraint() {
            const deviceId = document.getElementById('deviceSelect').value;
            if (!deviceId) {
                log('Please select a device first', 'error');
                return;
            }

            try {
                log(`Testing EXACT constraint with device: ${deviceId}`);
                
                const constraints = {
                    video: true,
                    audio: {
                        deviceId: { exact: deviceId },
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                };
                
                log(`Constraints: ${JSON.stringify(constraints.audio, null, 2)}`);
                
                if (currentStream) {
                    currentStream.getTracks().forEach(track => track.stop());
                }
                
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                document.getElementById('videoPreview').srcObject = currentStream;
                
                log('SUCCESS: Exact constraint worked!', 'success');
                
            } catch (error) {
                log(`FAILED: Exact constraint failed - ${error.name}: ${error.message}`, 'error');
                if (error.name === 'OverconstrainedError') {
                    log('This is the OverconstrainedError we were trying to fix!', 'error');
                }
            }
        }

        async function testIdealConstraint() {
            const deviceId = document.getElementById('deviceSelect').value;
            if (!deviceId) {
                log('Please select a device first', 'error');
                return;
            }

            try {
                log(`Testing IDEAL constraint with device: ${deviceId}`);
                
                const constraints = {
                    video: true,
                    audio: {
                        deviceId: { ideal: deviceId },
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                };
                
                log(`Constraints: ${JSON.stringify(constraints.audio, null, 2)}`);
                
                if (currentStream) {
                    currentStream.getTracks().forEach(track => track.stop());
                }
                
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                document.getElementById('videoPreview').srcObject = currentStream;
                
                // Check which device was actually used
                const audioTrack = currentStream.getAudioTracks()[0];
                if (audioTrack) {
                    const settings = audioTrack.getSettings();
                    log(`Actually using device: ${settings.deviceId}`, 'success');
                    if (settings.deviceId === deviceId) {
                        log('SUCCESS: Got the requested device!', 'success');
                    } else {
                        log('SUCCESS: Got a fallback device (this is expected behavior)', 'success');
                    }
                }
                
            } catch (error) {
                log(`FAILED: Ideal constraint failed - ${error.name}: ${error.message}`, 'error');
            }
        }

        async function testBasicConstraint() {
            try {
                log('Testing BASIC constraint (no device specification)');
                
                const constraints = {
                    video: true,
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                };
                
                log(`Constraints: ${JSON.stringify(constraints.audio, null, 2)}`);
                
                if (currentStream) {
                    currentStream.getTracks().forEach(track => track.stop());
                }
                
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                document.getElementById('videoPreview').srcObject = currentStream;
                
                // Check which device was actually used
                const audioTrack = currentStream.getAudioTracks()[0];
                if (audioTrack) {
                    const settings = audioTrack.getSettings();
                    log(`Using device: ${settings.deviceId}`, 'success');
                }
                
                log('SUCCESS: Basic constraint worked!', 'success');
                
            } catch (error) {
                log(`FAILED: Basic constraint failed - ${error.name}: ${error.message}`, 'error');
            }
        }

        function stopStream() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
                document.getElementById('videoPreview').srcObject = null;
                log('Stream stopped');
            } else {
                log('No active stream to stop');
            }
        }

        // Initialize
        log('Audio Constraints Test initialized');
        log('Click "Get Audio Devices" to start');
    </script>
</body>
</html>
